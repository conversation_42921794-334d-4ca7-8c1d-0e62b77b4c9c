export const HideEyeIcon = (svgProps) => {
  return (
    <svg viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg" {...svgProps}>
      <g id="SVGRepo_bgCarrier" strokeWidth={0} />
      <g
        id="SVGRepo_tracerCarrier"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <g id="SVGRepo_iconCarrier">
        <path
          fillRule="evenodd"
          clipRule="evenodd"
          d="M15.5778 13.6334C16.2396 12.1831 15.9738 10.4133 14.7803 9.21976C13.5868 8.02628 11.817 7.76042 10.3667 8.4222L11.5537 9.60918C12.315 9.46778 13.1307 9.69153 13.7196 10.2804C14.3085 10.8693 14.5323 11.6851 14.3909 12.4464L15.5778 13.6334Z"
        />{" "}
        <path
          fillRule="evenodd"
          clipRule="evenodd"
          d="M5.86339 7.80781C5.60443 8.02054 5.35893 8.23562 5.12798 8.44832C4.28009 9.22922 3.59623 10.0078 3.1244 10.5906C2.88801 10.8825 2.70365 11.1268 2.57733 11.2997C2.51414 11.3862 2.46539 11.4549 2.43184 11.5029C2.41506 11.5269 2.40207 11.5457 2.39297 11.559L2.38224 11.5747L2.37908 11.5794L2.37806 11.5809L2.09656 12L2.37741 12.4181L2.37806 12.4191L2.37908 12.4206L2.38224 12.4253L2.39297 12.441C2.40207 12.4543 2.41506 12.4731 2.43184 12.4971C2.46539 12.5451 2.51414 12.6138 2.57733 12.7003C2.70365 12.8732 2.88801 13.1175 3.1244 13.4094C3.59623 13.9922 4.28009 14.7708 5.12798 15.5517C6.79696 17.0888 9.22583 18.75 12 18.75C13.3694 18.75 14.6547 18.3452 15.806 17.7504L14.6832 16.6277C13.8289 17.0123 12.9256 17.25 12 17.25C9.80366 17.25 7.73254 15.9112 6.14416 14.4483C5.36337 13.7292 4.72921 13.0078 4.29019 12.4656C4.14681 12.2885 4.02475 12.1311 3.92572 12C4.02475 11.8689 4.14681 11.7115 4.29019 11.5344C4.72921 10.9922 5.36337 10.2708 6.14416 9.55168C6.39447 9.32114 6.65677 9.09369 6.92965 8.87408L5.86339 7.80781ZM17.0705 15.1258C17.3434 14.9063 17.6056 14.6788 17.8559 14.4483C18.6367 13.7292 19.2708 13.0078 19.7099 12.4656C19.8532 12.2885 19.9753 12.1311 20.0743 12C19.9753 11.8689 19.8532 11.7115 19.7099 11.5344C19.2708 10.9922 18.6367 10.2708 17.8559 9.55168C16.2675 8.08879 14.1964 6.75 12 6.75C11.0745 6.75 10.1712 6.98772 9.31694 7.37228L8.1942 6.24954C9.34544 5.65475 10.6307 5.25 12 5.25C14.7742 5.25 17.2031 6.91121 18.8721 8.44832C19.72 9.22922 20.4038 10.0078 20.8757 10.5906C21.112 10.8825 21.2964 11.1268 21.4227 11.2997C21.4859 11.3862 21.5347 11.4549 21.5682 11.5029C21.585 11.5269 21.598 11.5457 21.6071 11.559L21.6178 11.5747L21.621 11.5794L21.622 11.5809L21.9035 12L21.6224 12.4186L21.621 12.4206L21.6178 12.4253L21.6071 12.441C21.598 12.4543 21.585 12.4731 21.5682 12.4971C21.5347 12.5451 21.4859 12.6138 21.4227 12.7003C21.2964 12.8732 21.112 13.1175 20.8757 13.4094C20.4038 13.9922 19.72 14.7708 18.8721 15.5517C18.6412 15.7644 18.3957 15.9794 18.1368 16.1921L17.0705 15.1258Z"
        />{" "}
        <path
          fillRule="evenodd"
          clipRule="evenodd"
          d="M18.75 19.8107L3.75 4.81066L4.81066 3.75L19.8107 18.75L18.75 19.8107Z"
        />
      </g>
    </svg>
  );
};

export const SeeEyeIcon = (svgProps) => {
  return (
    <svg viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg" {...svgProps}>
      <g id="SVGRepo_bgCarrier" strokeWidth={0} />
      <g
        id="SVGRepo_tracerCarrier"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <g id="SVGRepo_iconCarrier">
        {" "}
        <path
          fillRule="evenodd"
          clipRule="evenodd"
          d="M12.0001 5.25C9.22586 5.25 6.79699 6.91121 5.12801 8.44832C4.28012 9.22922 3.59626 10.0078 3.12442 10.5906C2.88804 10.8825 2.70368 11.1268 2.57736 11.2997C2.51417 11.3862 2.46542 11.4549 2.43187 11.5029C2.41509 11.5269 2.4021 11.5457 2.393 11.559L2.38227 11.5747L2.37911 11.5794L2.10547 12.0132L2.37809 12.4191L2.37911 12.4206L2.38227 12.4253L2.393 12.441C2.4021 12.4543 2.41509 12.4731 2.43187 12.4971C2.46542 12.5451 2.51417 12.6138 2.57736 12.7003C2.70368 12.8732 2.88804 13.1175 3.12442 13.4094C3.59626 13.9922 4.28012 14.7708 5.12801 15.5517C6.79699 17.0888 9.22586 18.75 12.0001 18.75C14.7743 18.75 17.2031 17.0888 18.8721 15.5517C19.72 14.7708 20.4039 13.9922 20.8757 13.4094C21.1121 13.1175 21.2964 12.8732 21.4228 12.7003C21.4859 12.6138 21.5347 12.5451 21.5682 12.4971C21.585 12.4731 21.598 12.4543 21.6071 12.441L21.6178 12.4253L21.621 12.4206L21.6224 12.4186L21.9035 12L21.622 11.5809L21.621 11.5794L21.6178 11.5747L21.6071 11.559C21.598 11.5457 21.585 11.5269 21.5682 11.5029C21.5347 11.4549 21.4859 11.3862 21.4228 11.2997C21.2964 11.1268 21.1121 10.8825 20.8757 10.5906C20.4039 10.0078 19.72 9.22922 18.8721 8.44832C17.2031 6.91121 14.7743 5.25 12.0001 5.25ZM4.29022 12.4656C4.14684 12.2885 4.02478 12.1311 3.92575 12C4.02478 11.8689 4.14684 11.7115 4.29022 11.5344C4.72924 10.9922 5.36339 10.2708 6.14419 9.55168C7.73256 8.08879 9.80369 6.75 12.0001 6.75C14.1964 6.75 16.2676 8.08879 17.8559 9.55168C18.6367 10.2708 19.2709 10.9922 19.7099 11.5344C19.8533 11.7115 19.9753 11.8689 20.0744 12C19.9753 12.1311 19.8533 12.2885 19.7099 12.4656C19.2709 13.0078 18.6367 13.7292 17.8559 14.4483C16.2676 15.9112 14.1964 17.25 12.0001 17.25C9.80369 17.25 7.73256 15.9112 6.14419 14.4483C5.36339 13.7292 4.72924 13.0078 4.29022 12.4656ZM14.25 12C14.25 13.2426 13.2427 14.25 12 14.25C10.7574 14.25 9.75005 13.2426 9.75005 12C9.75005 10.7574 10.7574 9.75 12 9.75C13.2427 9.75 14.25 10.7574 14.25 12ZM15.75 12C15.75 14.0711 14.0711 15.75 12 15.75C9.92898 15.75 8.25005 14.0711 8.25005 12C8.25005 9.92893 9.92898 8.25 12 8.25C14.0711 8.25 15.75 9.92893 15.75 12Z"
        />{" "}
      </g>
    </svg>
  );
};

export const GoogleIcon = (props) => (
  <svg
    width={28}
    height={28}
    viewBox="0 0 28 28"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    {...props}
  >
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M27.0772 14.3097C27.0772 13.3438 26.9906 12.415 26.8296 11.5234H14.0005V16.7925H21.3314C21.0156 18.4952 20.0559 19.9379 18.6133 20.9038V24.3216H23.0155C25.5912 21.9502 27.0772 18.4581 27.0772 14.3097Z"
      fill="#4285F4"
    />
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M14.0009 27.6234C17.6787 27.6234 20.7622 26.4036 23.0159 24.3232L18.6137 20.9054C17.3939 21.7227 15.8336 22.2057 14.0009 22.2057C10.4531 22.2057 7.45014 19.8095 6.37899 16.5898H1.82812V20.1191C4.0695 24.5709 8.67609 27.6234 14.0009 27.6234Z"
      fill="#34A853"
    />
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M6.37837 16.5902C6.10594 15.7729 5.95114 14.8999 5.95114 14.0021C5.95114 13.1043 6.10594 12.2313 6.37837 11.414V7.88477H1.82751C0.904952 9.72368 0.378662 11.8041 0.378662 14.0021C0.378662 16.2001 0.904952 18.2805 1.82751 20.1195L6.37837 16.5902Z"
      fill="#FBBC05"
    />
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M14.0009 5.7966C16.0008 5.7966 17.7964 6.48387 19.2081 7.83365L23.115 3.92672C20.756 1.72869 17.6725 0.378906 14.0009 0.378906C8.67608 0.378906 4.0695 3.43139 1.82812 7.88318L6.37898 11.4124C7.45014 8.19276 10.4531 5.7966 14.0009 5.7966Z"
      fill="#EA4335"
    />
  </svg>
);

export const LinkedInIcon = (props) => (
  <svg
    width={24}
    height={24}
    viewBox="0 0 24 24"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    {...props}
  >
    <path
      d="M21.0801 21.0805V14.4286C21.0801 11.1594 20.3763 8.66211 16.5622 8.66211C14.7233 8.66211 13.4974 9.66103 12.9979 10.6145H12.9525V8.95724H9.34277V21.0805H13.1114V15.0643C13.1114 13.4751 13.4066 11.954 15.359 11.954C17.2887 11.954 17.3114 13.7475 17.3114 15.1551V21.0578H21.0801V21.0805Z"
      fill="#006EAD"
    />
    <path
      d="M3.21313 8.95703H6.98178V21.0803H3.21313V8.95703Z"
      fill="#006EAD"
    />
    <path
      d="M5.09767 2.91797C3.89443 2.91797 2.91821 3.89419 2.91821 5.09743C2.91821 6.30067 3.89443 7.29959 5.09767 7.29959C6.30092 7.29959 7.27713 6.30067 7.27713 5.09743C7.27713 3.89419 6.30092 2.91797 5.09767 2.91797Z"
      fill="#006EAD"
    />
  </svg>
);

export const AppleIcon = (props) => (
  <svg
    width={22}
    height={22}
    viewBox="0 0 22 22"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    {...props}
  >
    <g clipPath="url(#clip0_3249_17516)">
      <path
        d="M15.1455 0.228516C15.1957 0.228516 15.2458 0.228516 15.2988 0.228516C15.4217 1.7477 14.8419 2.88284 14.1371 3.70487C13.4457 4.52122 12.4988 5.31298 10.9673 5.19284C10.8651 3.69541 11.4459 2.64446 12.1497 1.82433C12.8024 1.06 13.999 0.379867 15.1455 0.228516Z"
        fill="black"
      />
      <path
        d="M19.7817 16.0416C19.7817 16.0567 19.7817 16.07 19.7817 16.0842C19.3513 17.3877 18.7373 18.5048 17.9882 19.5416C17.3042 20.4828 16.4661 21.7494 14.9696 21.7494C13.6765 21.7494 12.8176 20.918 11.4923 20.8952C10.0905 20.8725 9.31951 21.5905 8.03775 21.7712C7.89113 21.7712 7.74451 21.7712 7.60073 21.7712C6.65951 21.635 5.89991 20.8896 5.34654 20.218C3.71478 18.2334 2.45383 15.6698 2.21924 12.3893C2.21924 12.0677 2.21924 11.747 2.21924 11.4254C2.31856 9.07755 3.45937 7.16863 4.97572 6.24349C5.776 5.7516 6.87613 5.33255 8.10113 5.51984C8.62613 5.6012 9.16248 5.78093 9.63262 5.95876C10.0782 6.12998 10.6353 6.43363 11.1632 6.41755C11.5207 6.40714 11.8764 6.22079 12.2368 6.0893C13.2925 5.70809 14.3273 5.27106 15.6914 5.47633C17.3307 5.72417 18.4942 6.45255 19.2132 7.57633C17.8264 8.4589 16.7301 9.7889 16.9173 12.0601C17.0838 14.1232 18.2833 15.3302 19.7817 16.0416Z"
        fill="black"
      />
    </g>
    <defs>
      <clipPath id="clip0_3249_17516">
        <rect
          width={21.542}
          height={21.542}
          fill="white"
          transform="translate(0.229004 0.228516)"
        />
      </clipPath>
    </defs>
  </svg>
);

export const Edit2Icon = ({ size = 24, stroke = "#fff" }) => {
  return (
    <svg
      width={size}
      height={size}
      viewBox="0 0 36 36"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M26.748 20.1875C26.748 31.0736 26.6846 31.125 15.8105 31.125C4.93648 31.125 4.87305 31.0922 4.87305 20.1875C4.87305 9.28281 4.89383 9.25 15.8105 9.25"
        stroke={stroke}
        strokeWidth="2.1875"
        strokeMiterlimit="1"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M14.7168 18V21.2812H17.998L31.123 8.15625L27.8418 4.875L14.7168 18Z"
        stroke={stroke}
        strokeWidth="2.1875"
        strokeMiterlimit="1"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M25.1094 8.70312L27.2969 10.8906"
        stroke={stroke}
        strokeWidth="2.1875"
        strokeMiterlimit="1"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  );
};

export const Collapsed2Icon = () => {
  return (
    <svg
      width={24}
      height={24}
      viewBox="0 0 24 24"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M15 1.25H9C3.567 1.25 1.25 3.567 1.25 9V15C1.25 20.433 3.567 22.75 9 22.75H15C20.433 22.75 22.75 20.433 22.75 15V9C22.75 3.567 20.433 1.25 15 1.25ZM14.25 21.25H9C4.386 21.25 2.75 19.614 2.75 15V9C2.75 4.386 4.386 2.75 9 2.75H14.25V21.25ZM21.25 15C21.25 19.354 19.791 21.054 15.75 21.232V2.768C19.791 2.946 21.25 4.646 21.25 9V15ZM8.53 8.91L11.09 11.47C11.1597 11.5396 11.2149 11.6222 11.2526 11.7131C11.2903 11.8041 11.3098 11.9016 11.3098 12C11.3098 12.0984 11.2903 12.1959 11.2526 12.2869C11.2149 12.3778 11.1597 12.4604 11.09 12.53L8.53 15.09C8.38848 15.2266 8.199 15.3021 8.00235 15.3003C7.8057 15.2985 7.61763 15.2195 7.47864 15.0804C7.33965 14.9412 7.26086 14.7531 7.25924 14.5565C7.25763 14.3598 7.33331 14.1704 7.47 14.029L9.5 12L7.47 9.971C7.33331 9.82961 7.25763 9.6402 7.25924 9.44355C7.26086 9.2469 7.33965 9.05875 7.47864 8.91963C7.61763 8.78051 7.8057 8.70154 8.00235 8.69974C8.199 8.69794 8.38848 8.77345 8.53 8.91Z"
        fill="#1165EF"
      />
    </svg>
  );
};

export const HomeIcon = ({
  size = 18,
  stroke = "#fff",
  fill = "#00000000",
  className = "",
}) => {
  // console.log(stroke);
  return (
    <svg
      width={size}
      className={className}
      height={size}
      viewBox="0 0 24 24"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M2 18C2 16.4596 2 15.6893 2.34673 15.1235C2.54074 14.8069 2.80693 14.5407 3.12353 14.3467C3.68934 14 4.45956 14 6 14C7.54044 14 8.31066 14 8.87647 14.3467C9.19307 14.5407 9.45926 14.8069 9.65327 15.1235C10 15.6893 10 16.4596 10 18C10 19.5404 10 20.3107 9.65327 20.8765C9.45926 21.1931 9.19307 21.4593 8.87647 21.6533C8.31066 22 7.54044 22 6 22C4.45956 22 3.68934 22 3.12353 21.6533C2.80693 21.4593 2.54074 21.1931 2.34673 20.8765C2 20.3107 2 19.5404 2 18Z"
        fill={fill}
        stroke={stroke}
        strokeWidth="1.5"
        className={className}
      />
      <path
        d="M14 18C14 16.4596 14 15.6893 14.3467 15.1235C14.5407 14.8069 14.8069 14.5407 15.1235 14.3467C15.6893 14 16.4596 14 18 14C19.5404 14 20.3107 14 20.8765 14.3467C21.1931 14.5407 21.4593 14.8069 21.6533 15.1235C22 15.6893 22 16.4596 22 18C22 19.5404 22 20.3107 21.6533 20.8765C21.4593 21.1931 21.1931 21.4593 20.8765 21.6533C20.3107 22 19.5404 22 18 22C16.4596 22 15.6893 22 15.1235 21.6533C14.8069 21.4593 14.5407 21.1931 14.3467 20.8765C14 20.3107 14 19.5404 14 18Z"
        fill={fill}
        stroke={stroke}
        strokeWidth="1.5"
        className={className}
      />
      <path
        d="M2 6C2 4.45956 2 3.68934 2.34673 3.12353C2.54074 2.80693 2.80693 2.54074 3.12353 2.34673C3.68934 2 4.45956 2 6 2C7.54044 2 8.31066 2 8.87647 2.34673C9.19307 2.54074 9.45926 2.80693 9.65327 3.12353C10 3.68934 10 4.45956 10 6C10 7.54044 10 8.31066 9.65327 8.87647C9.45926 9.19307 9.19307 9.45926 8.87647 9.65327C8.31066 10 7.54044 10 6 10C4.45956 10 3.68934 10 3.12353 9.65327C2.80693 9.45926 2.54074 9.19307 2.34673 8.87647C2 8.31066 2 7.54044 2 6Z"
        fill={fill}
        stroke={stroke}
        strokeWidth="1.5"
        className={className}
      />
      <path
        d="M14 6C14 4.45956 14 3.68934 14.3467 3.12353C14.5407 2.80693 14.8069 2.54074 15.1235 2.34673C15.6893 2 16.4596 2 18 2C19.5404 2 20.3107 2 20.8765 2.34673C21.1931 2.54074 21.4593 2.80693 21.6533 3.12353C22 3.68934 22 4.45956 22 6C22 7.54044 22 8.31066 21.6533 8.87647C21.4593 9.19307 21.1931 9.45926 20.8765 9.65327C20.3107 10 19.5404 10 18 10C16.4596 10 15.6893 10 15.1235 9.65327C14.8069 9.45926 14.5407 9.19307 14.3467 8.87647C14 8.31066 14 7.54044 14 6Z"
        fill={fill}
        stroke={stroke}
        strokeWidth="1.5"
        className={className}
      />
    </svg>
  );
};

export const DotMenuIcon = ({
  size = 18,
  stroke = "#fff",
  className = "",
  fill = "none",
}) => {
  return (
    <svg
      width={size}
      height={size}
      viewBox="0 0 18 18"
      fill={fill}
      xmlns="http://www.w3.org/2000/svg"
      className={className}
    >
      <path
        d="M11.25 3H15.75"
        stroke={stroke}
        className={className}
        strokeWidth={1.125}
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M11.25 11.25H15.75"
        stroke={stroke}
        className={className}
        strokeWidth={1.125}
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M11.25 6.75H15.75"
        stroke={stroke}
        className={className}
        strokeWidth={1.125}
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M11.25 15H15.75"
        stroke={stroke}
        className={className}
        strokeWidth={1.125}
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <circle
        cx={4.875}
        cy={4.875}
        r={2.625}
        stroke={stroke}
        className={className}
        fill={fill}
        strokeWidth={1.125}
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <circle
        cx={4.875}
        cy={13.125}
        r={2.625}
        stroke={stroke}
        className={className}
        fill={fill}
        strokeWidth={1.125}
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  );
};

export const UserGroupIcon = ({
  size = 18,
  stroke = "#fff",
  className = "",
  fill = "none",
}) => {
  return (
    <svg
      width={size}
      height={size}
      viewBox="0 0 20 20"
      fill={fill}
      xmlns="http://www.w3.org/2000/svg"
    >
      <g clipPath="url(#clip0_4046_14652)">
        <path
          d="M17.3117 15C17.9361 15 18.4328 14.6071 18.8787 14.0576C19.7916 12.9329 18.2928 12.034 17.7211 11.5938C17.14 11.1463 16.4912 10.8928 15.8333 10.8333M15 9.16667C16.1506 9.16667 17.0833 8.23393 17.0833 7.08333C17.0833 5.93274 16.1506 5 15 5"
          stroke={stroke}
          className={className}
          strokeWidth={1.15385}
          strokeLinecap="round"
        />
        <path
          d="M2.68822 15C2.0638 15 1.56714 14.6071 1.12121 14.0576C0.208326 12.9329 1.70714 12.034 2.27879 11.5938C2.8599 11.1463 3.50874 10.8928 4.16658 10.8333M4.58325 9.16667C3.43266 9.16667 2.49992 8.23393 2.49992 7.08333C2.49992 5.93274 3.43266 5 4.58325 5"
          stroke={stroke}
          className={className}
          strokeWidth={1.15385}
          strokeLinecap="round"
        />
        <path
          d="M6.73642 12.593C5.88494 13.1195 3.65241 14.1946 5.01217 15.5398C5.6764 16.197 6.41619 16.667 7.34627 16.667H12.6536C13.5837 16.667 14.3234 16.197 14.9877 15.5398C16.3474 14.1946 14.1149 13.1195 13.2634 12.593C11.2667 11.3583 8.73313 11.3583 6.73642 12.593Z"
          stroke={stroke}
          className={className}
          strokeWidth={1.15385}
          strokeLinecap="round"
          strokeLinejoin="round"
        />
        <path
          d="M12.9166 6.24967C12.9166 7.8605 11.6107 9.16634 9.99992 9.16634C8.38909 9.16634 7.08325 7.8605 7.08325 6.24967C7.08325 4.63884 8.38909 3.33301 9.99992 3.33301C11.6107 3.33301 12.9166 4.63884 12.9166 6.24967Z"
          stroke={stroke}
          className={className}
          strokeWidth={1.15385}
        />
      </g>
      <defs>
        <clipPath id="clip0_4046_14652">
          <rect width={20} height={20} fill="white" />
        </clipPath>
      </defs>
    </svg>
  );
};

export const FolderIcon = ({
  size = 20,
  stroke = "#fff",
  className,
  fill = "none",
}) => {
  return (
    <svg
      width={size}
      height={size}
      viewBox="0 0 20 20"
      fill={fill}
      xmlns="http://www.w3.org/2000/svg"
      className={className}
    >
      <path
        d="M6.04159 6.04167H13.7603C15.4281 6.04167 16.262 6.04167 16.8611 6.44193C17.1204 6.6152 17.343 6.83786 17.5163 7.09719C17.9166 7.69622 17.9166 8.53012 17.9166 10.1979C17.9166 12.9776 17.9166 14.3674 17.2495 15.3658C16.9607 15.798 16.5896 16.1691 16.1574 16.4579C15.159 17.125 13.7692 17.125 10.9895 17.125H9.99992C6.26797 17.125 4.40199 17.125 3.24262 15.9656C2.08325 14.8063 2.08325 12.9403 2.08325 9.20833V6.78922C2.08325 5.35117 2.08325 4.63215 2.38434 4.09263C2.59896 3.70804 2.9163 3.39071 3.30089 3.17608C3.84041 2.875 4.55943 2.875 5.99747 2.875C6.91877 2.875 7.37942 2.875 7.78266 3.02621C8.70335 3.37147 9.083 4.20783 9.49845 5.03872L9.99992 6.04167"
        stroke={stroke}
        strokeWidth={1.1875}
        strokeLinecap="round"
        className={className}
      />
    </svg>
  );
};

export const BriefCaseIcon = ({ size = 18, stroke = "#fff" }) => {
  return (
    <svg
      width={size}
      height={size}
      viewBox="0 0 19 19"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <g clipPath="url(#clip0_4046_14637)">
        <path
          d="M5.42798 4.78809V3.22265C5.42798 2.59961 5.93774 2.08984 6.56078 2.08984H12.4514C13.0745 2.08984 13.5842 2.59961 13.5842 3.22265V4.78809"
          stroke={stroke}
          strokeWidth={1.05469}
          strokeMiterlimit={22.9256}
          strokeLinecap="round"
          strokeLinejoin="round"
        />
        <path
          d="M17.2744 9.84961V15.8525C17.2744 16.4325 16.7997 16.9072 16.2197 16.9072H2.79004C2.21007 16.9072 1.73535 16.4326 1.73535 15.8525V9.88477"
          stroke={stroke}
          strokeWidth={1.05469}
          strokeMiterlimit={22.9256}
          strokeLinecap="round"
          strokeLinejoin="round"
        />
        <path
          d="M8.09863 11.627C4.37949 11.4143 1.03223 10.3508 1.03223 8.43642V5.94043C1.03223 5.35954 1.50599 4.88574 2.08691 4.88574H16.9229C17.5037 4.88574 17.9775 5.35958 17.9775 5.94043V8.43642C17.9775 10.3628 14.588 11.4276 10.8408 11.6309"
          stroke={stroke}
          strokeWidth={1.05469}
          strokeMiterlimit={22.9256}
          strokeLinecap="round"
          strokeLinejoin="round"
        />
        <path
          fillRule="evenodd"
          clipRule="evenodd"
          d="M8.2041 10.5811H10.8057V11.7939C10.8057 12.461 10.2599 13.0068 9.59277 13.0068H9.41699C8.7499 13.0068 8.2041 12.461 8.2041 11.7939V10.5811Z"
          stroke={stroke}
          strokeWidth={1.05469}
          strokeMiterlimit={22.9256}
          strokeLinecap="round"
          strokeLinejoin="round"
        />
      </g>
      <defs>
        <clipPath id="clip0_4046_14637">
          <rect
            width={18}
            height={18}
            fill="white"
            transform="translate(0.5 0.5)"
          />
        </clipPath>
      </defs>
    </svg>
  );
};

export const NotificationIcon = ({ size = 22 }) => {
  return (
    <svg
      width={size}
      height={size}
      viewBox="0 0 26 26"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M2.74067 15.9998C2.51029 17.51 3.54025 18.5582 4.80131 19.0806C9.63596 21.0834 16.3639 21.0834 21.1985 19.0806C22.4596 18.5582 23.4895 17.51 23.2592 15.9998C23.1176 15.0717 22.4175 14.2988 21.8988 13.5442C21.2195 12.5436 21.152 11.4522 21.1519 10.291C21.1519 5.8037 17.5021 2.16602 12.9999 2.16602C8.49772 2.16602 4.84797 5.8037 4.84797 10.291C4.84787 11.4522 4.78037 12.5436 4.10099 13.5442C3.58233 14.2988 2.88225 15.0717 2.74067 15.9998Z"
        stroke="#2D394A"
        strokeWidth="1.8"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M8.66675 20.582C9.16344 22.451 10.9152 23.832 13.0001 23.832C15.0849 23.832 16.8367 22.451 17.3334 20.582"
        stroke="#2D394A"
        strokeWidth="1.8"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  );
};

export const EditPencilIcon = (props) => {
  return (
    <svg
      width={18}
      height={18}
      viewBox="0 0 18 18"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...props}
    >
      <g clipPath="url(#clip0_3597_27635)">
        <path
          d="M12.7099 2.38076C13.1504 1.94031 13.3706 1.72009 13.6146 1.61477C13.9661 1.46304 14.3646 1.46304 14.7161 1.61477C14.96 1.72009 15.1802 1.94031 15.6207 2.38076C16.0612 2.82122 16.2814 3.04144 16.3867 3.28541C16.5384 3.6369 16.5384 4.03541 16.3867 4.38689C16.2814 4.63087 16.0612 4.85109 15.6207 5.29154L11.8545 9.05774C10.9266 9.98563 10.4627 10.4496 9.88159 10.7245C9.30051 10.9994 8.64756 11.0638 7.34166 11.1926L6.75049 11.251L6.80882 10.6598C6.93768 9.35391 7.0021 8.70096 7.277 8.11988C7.55189 7.5388 8.01584 7.07485 8.94373 6.14696L12.7099 2.38076Z"
          stroke="white"
          strokeWidth={1.57526}
          strokeLinejoin="round"
        />
        <path
          d="M4.49902 11.252H2.81152C2.08665 11.252 1.49902 11.8396 1.49902 12.5645C1.49902 13.2893 2.08665 13.877 2.81152 13.877H9.93652C10.6614 13.877 11.249 14.4646 11.249 15.1895C11.249 15.9143 10.6614 16.502 9.93652 16.502H8.24902"
          stroke="white"
          strokeWidth={1.57526}
          strokeLinecap="round"
          strokeLinejoin="round"
        />
      </g>
      <defs>
        <clipPath id="clip0_3597_27635">
          <rect width={18} height={18} fill="white" />
        </clipPath>
      </defs>
    </svg>
  );
};

export const ThreeDotMenuIcon = ({ size = 26, fill = "#787E89" }) => (
  <svg
    width={size}
    height={size}
    viewBox="0 0 26 26"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <circle cx={5.02968} cy={12.4767} r={2.32143} fill={fill} />
    <circle cx={12.458} cy={12.4767} r={2.32143} fill={fill} />
    <circle cx={19.8873} cy={12.4767} r={2.32143} fill={fill} />
  </svg>
);

export const HeartIcon = ({ stroke = "#787E89", size = 26 }) => (
  <svg
    width={size}
    height={size}
    viewBox="0 0 26 26"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M13.3713 21.9353C13.0125 22.0619 12.4217 22.0619 12.063 21.9353C9.00325 20.8908 2.16638 16.5334 2.16638 9.14785C2.16638 5.88768 4.79351 3.25 8.03258 3.25C9.95282 3.25 11.6515 4.17846 12.7171 5.61336C13.7827 4.17846 15.4919 3.25 17.4016 3.25C20.6407 3.25 23.2678 5.88768 23.2678 9.14785C23.2678 16.5334 16.431 20.8908 13.3713 21.9353Z"
      stroke={stroke}
      strokeWidth={1.63333}
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </svg>
);

export const HeartFillOutIcon = ({ fill = "#EF3B41", size = 26 }) => (
  <svg
    width={size}
    height={size}
    viewBox="0 0 26 26"
    fill={fill}
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M13.3713 21.9353C13.0125 22.0619 12.4217 22.0619 12.063 21.9353C9.00325 20.8908 2.16638 16.5334 2.16638 9.14785C2.16638 5.88768 4.79351 3.25 8.03258 3.25C9.95282 3.25 11.6515 4.17846 12.7171 5.61336C13.7827 4.17846 15.4919 3.25 17.4016 3.25C20.6407 3.25 23.2678 5.88768 23.2678 9.14785C23.2678 16.5334 16.431 20.8908 13.3713 21.9353Z"
      stroke="none"
      strokeWidth={1.63333}
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </svg>
);

export const MessageIcon = ({ stroke = "#787E89", size = 26 }) => (
  <svg
    width={size}
    height={size}
    viewBox="0 0 26 26"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M9.10308 14.543H16.9031M9.10308 9.66797H13.0031"
      stroke={stroke}
      strokeWidth={1.4625}
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M7.24634 19.9078C5.97873 19.7831 5.02914 19.4024 4.39228 18.7655C3.25 17.6232 3.25 15.7848 3.25 12.1078V11.6203C3.25 7.94336 3.25 6.10488 4.39228 4.9626C5.53457 3.82031 7.37305 3.82031 11.05 3.82031H14.95C18.627 3.82031 20.4654 3.82031 21.6077 4.9626C22.75 6.10488 22.75 7.94336 22.75 11.6203V12.1078C22.75 15.7848 22.75 17.6232 21.6077 18.7655C20.4654 19.9078 18.627 19.9078 14.95 19.9078C14.4035 19.92 13.9683 19.9616 13.5407 20.059C12.3723 20.328 11.2904 20.9258 10.2211 21.4472C8.69761 22.1901 7.93585 22.5615 7.4578 22.2138C6.54325 21.5327 7.43718 19.4221 7.6375 18.4453"
      stroke={stroke}
      strokeWidth={1.4625}
      strokeLinecap="round"
    />
  </svg>
);

export const MeassageFillOutIcon = (props) => (
  <svg
    width={26}
    height={26}
    viewBox="0 0 26 26"
    fill="#6d11d2"
    xmlns="http://www.w3.org/2000/svg"
    {...props}
  >
    <path
      d="M9.10308 14.543H16.9031M9.10308 9.66797H13.0031"
      // stroke="none"
      stroke="#6d11d2"
      strokeWidth={1.4625}
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M7.24634 19.9078C5.97873 19.7831 5.02914 19.4024 4.39228 18.7655C3.25 17.6232 3.25 15.7848 3.25 12.1078V11.6203C3.25 7.94336 3.25 6.10488 4.39228 4.9626C5.53457 3.82031 7.37305 3.82031 11.05 3.82031H14.95C18.627 3.82031 20.4654 3.82031 21.6077 4.9626C22.75 6.10488 22.75 7.94336 22.75 11.6203V12.1078C22.75 15.7848 22.75 17.6232 21.6077 18.7655C20.4654 19.9078 18.627 19.9078 14.95 19.9078C14.4035 19.92 13.9683 19.9616 13.5407 20.059C12.3723 20.328 11.2904 20.9258 10.2211 21.4472C8.69761 22.1901 7.93585 22.5615 7.4578 22.2138C6.54325 21.5327 7.43718 19.4221 7.6375 18.4453"
      // stroke="none"
      stroke="#6d11d2"
      strokeWidth={1.4625}
      strokeLinecap="round"
    />
  </svg>
);

export const BookmarkIcon = ({ size = 22, stroke = "#787E89" }) => (
  <svg
    width={size}
    height={size}
    viewBox="0 0 22 22"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M3.7771 16.3998V8.93084C3.7771 5.65072 3.7771 4.01066 4.83477 2.99166C5.89244 1.97266 7.59473 1.97266 10.9993 1.97266C14.4039 1.97266 16.1062 1.97266 17.1639 2.99166C18.2215 4.01066 18.2215 5.65072 18.2215 8.93084V16.3998C18.2215 18.4815 18.2215 19.5223 17.5238 19.8948C16.1727 20.6163 13.6383 18.2093 12.4347 17.4846C11.7367 17.0642 11.3877 16.8541 10.9993 16.8541C10.611 16.8541 10.2619 17.0642 9.56391 17.4846C8.36032 18.2093 5.82593 20.6163 4.47481 19.8948C3.7771 19.5223 3.7771 18.4815 3.7771 16.3998Z"
      stroke={stroke}
      strokeWidth={1.35417}
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </svg>
);

export const BookmarkFillOutIcon = (props) => (
  <svg
    width={22}
    height={22}
    viewBox="0 0 22 22"
    fill="#6d11d2"
    xmlns="http://www.w3.org/2000/svg"
    {...props}
  >
    <path
      d="M3.7771 16.3998V8.93084C3.7771 5.65072 3.7771 4.01066 4.83477 2.99166C5.89244 1.97266 7.59473 1.97266 10.9993 1.97266C14.4039 1.97266 16.1062 1.97266 17.1639 2.99166C18.2215 4.01066 18.2215 5.65072 18.2215 8.93084V16.3998C18.2215 18.4815 18.2215 19.5223 17.5238 19.8948C16.1727 20.6163 13.6383 18.2093 12.4347 17.4846C11.7367 17.0642 11.3877 16.8541 10.9993 16.8541C10.611 16.8541 10.2619 17.0642 9.56391 17.4846C8.36032 18.2093 5.82593 20.6163 4.47481 19.8948C3.7771 19.5223 3.7771 18.4815 3.7771 16.3998Z"
      stroke="none"
      strokeWidth={1.35417}
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </svg>
);

export const UserProfileIcon = (props) => (
  <svg
    width={38}
    height={39}
    viewBox="0 0 38 39"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    {...props}
  >
    <path
      d="M10.5651 24.999C8.36436 26.3094 2.59412 28.9852 6.10858 32.3334C7.82537 33.969 9.73743 35.1388 12.1414 35.1388H25.8586C28.2626 35.1388 30.1746 33.969 31.8914 32.3334C35.4059 28.9852 29.6356 26.3094 27.4349 24.999C22.2742 21.9261 15.7258 21.9261 10.5651 24.999Z"
      stroke="#2D394A"
      strokeWidth={2.33333}
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M26 11.0283C26 14.8943 22.866 18.0283 19 18.0283C15.134 18.0283 12 14.8943 12 11.0283C12 7.16233 15.134 4.02832 19 4.02832C22.866 4.02832 26 7.16233 26 11.0283Z"
      stroke="#2D394A"
      strokeWidth={2.33333}
    />
  </svg>
);

export const EditPencileIcon = (props) => (
  <svg
    width={20}
    height={20}
    viewBox="0 0 20 20"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    {...props}
  >
    <path
      d="M11.6021 3.7294C12.178 3.10552 12.4659 2.79359 12.7718 2.61163C13.51 2.17259 14.419 2.15894 15.1696 2.57562C15.4806 2.74831 15.7774 3.05146 16.3709 3.65778C16.9644 4.26409 17.2612 4.56725 17.4302 4.88499C17.8381 5.65169 17.8248 6.58025 17.395 7.33437C17.2169 7.6469 16.9115 7.94101 16.3008 8.52924L9.03431 15.5281C7.87696 16.6428 7.29828 17.2001 6.57505 17.4826C5.85183 17.7651 5.05675 17.7443 3.46661 17.7027L3.25025 17.6971C2.76616 17.6844 2.52411 17.6781 2.38341 17.5184C2.24271 17.3587 2.26192 17.1122 2.30034 16.619L2.3212 16.3513C2.42933 14.9634 2.4834 14.2694 2.75442 13.6456C3.02544 13.0218 3.49293 12.5153 4.42793 11.5023L11.6021 3.7294Z"
      stroke="white"
      strokeWidth={2}
      strokeLinejoin="round"
    />
    <path
      d="M10.7725 3.81836L16.1816 9.22745"
      stroke="white"
      strokeWidth={2}
      strokeLinejoin="round"
    />
  </svg>
);

export const AddProjectIcon = ({ size = 18, stroke = "#fff" }) => {
  return (
    <svg
      width={size}
      height={size}
      viewBox="0 0 18 18"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <g clipPath="url(#clip0_3597_27645)">
        <path
          d="M13.5 10.125V16.125M16.5 13.125L10.5 13.125"
          stroke={stroke}
          strokeWidth="1.8"
          strokeLinecap="round"
        />
        <path
          d="M5.25 4.875H12.5625C14.1425 4.875 14.9325 4.875 15.5 5.25419C15.7457 5.41835 15.9566 5.62929 16.1208 5.87497C16.469 6.39614 16.4973 7.26312 16.4995 8.625M9 4.875L8.52492 3.92484C8.13134 3.13768 7.77167 2.34534 6.89944 2.01826C6.51742 1.875 6.08102 1.875 5.2082 1.875C3.84585 1.875 3.16467 1.875 2.65355 2.16024C2.2892 2.36357 1.98857 2.6642 1.78524 3.02855C1.5 3.53967 1.5 4.22085 1.5 5.5832V7.875C1.5 11.4105 1.5 13.1783 2.59835 14.2766C3.61612 15.2944 5.2087 15.3691 8.25 15.3746"
          stroke={stroke}
          strokeWidth="1.8"
          strokeLinecap="round"
        />
      </g>
      <defs>
        <clipPath id="clip0_3597_27645">
          <rect width="18" height="18" fill="white" />
        </clipPath>
      </defs>
    </svg>
  );
};

export const EmptyDataIcon = ({ width = 261, height = 248 }) => {
  return (
    <svg
      width={width}
      height={height}
      viewBox="0 0 261 248"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <g clipPath="url(#clip0_228_404)">
        <path
          d="M248.178 58.8798C248.305 58.693 251.463 61.8725 254.336 61.2182C257.914 60.4031 260.532 53.7563 258.591 50.9133C257.307 49.0319 254.113 48.9434 254.18 48.5905C254.251 48.2129 257.688 49.4787 259.558 47.9669C261.829 46.1304 261.355 40.5449 258.576 37.6153C258.02 37.0292 257.096 36.0555 255.77 35.9784C253.164 35.8269 251.119 39.2569 250.22 40.7645C247.362 45.5577 247.128 50.5864 246.775 50.5393C246.449 50.4958 247.315 46.2875 245.746 45.4259C245.403 45.2372 244.994 45.2398 244.589 45.3336C244.691 45.068 244.765 44.7947 244.802 44.5136C245.038 42.7413 243.63 40.8573 241.966 39.2779C243.699 40.3044 245.657 41.0162 247.39 40.507C251.261 39.3696 253.837 32.1919 251.802 27.7858C250.38 24.7084 246.756 23.0824 246.875 22.9062C247.002 22.7194 250.161 25.8989 253.033 25.2446C256.612 24.4296 259.229 17.7828 257.289 14.9398C256.005 13.0583 252.811 12.9698 252.877 12.6169C252.948 12.2394 256.385 13.5051 258.255 11.9933C260.527 10.1568 260.052 4.57138 257.273 1.64175C256.717 1.05557 255.793 0.081953 254.467 0.00485803C251.862 -0.146613 249.816 3.28336 248.917 4.79097C246.059 9.5841 245.825 14.6128 245.472 14.5658C245.146 14.5222 246.012 10.314 244.443 9.45233C243.461 8.9128 241.951 9.91927 241.482 10.2319C236.322 13.6692 237.752 21.1436 232.986 29.0955C231.451 31.6559 229.922 33.1847 228.632 34.3412C228.043 27.2215 230.213 21.7561 226.925 17.6163C226.574 17.1748 225.445 15.7534 224.345 15.9656C222.588 16.3045 222.121 20.5753 221.797 20.5168C221.447 20.4534 222.767 15.5953 221.517 10.1564C221.124 8.44565 220.229 4.55356 217.703 3.89844C216.417 3.56488 215.239 4.20819 214.53 4.59549C210.987 6.53121 208.821 11.7017 210.42 14.1465C211.736 16.1591 215.396 16.009 215.347 16.3902C215.302 16.7464 212.235 15.8507 210.436 17.2474C207.717 19.358 208.169 26.4874 211.325 28.361C213.858 29.8651 217.84 27.8079 217.903 28.0246C217.963 28.2289 214.014 28.6645 211.718 31.1574C210.764 32.1923 210.11 33.5685 209.74 35.0554C206.613 35.2905 203.936 37.5319 203.835 37.3592C203.722 37.1639 207.956 35.6968 208.613 32.8246C209.43 29.2465 204.577 24.0051 201.173 24.5207C198.921 24.862 197.456 27.7017 197.167 27.4888C196.858 27.261 199.489 24.7126 198.938 22.372C198.268 19.5286 193.03 17.534 189.185 18.7678C188.415 19.0146 187.138 19.4246 186.493 20.5864C185.227 22.8684 187.43 26.199 188.399 27.6629C191.478 32.3172 195.907 34.7091 195.712 35.0064C195.531 35.2813 192.115 32.6763 190.658 33.716C189.746 34.367 189.998 36.1647 190.076 36.7231C190.936 42.8629 198.29 44.816 203.388 52.5595C207.539 58.8651 205.768 62.2874 209.285 63.9965C210.795 64.7302 212.717 64.8721 214.682 64.5188C213.162 74.7021 212.732 92.1178 212.732 92.1178C212.303 109.538 210.063 123.885 209.083 123.857C208.539 123.84 208.739 119.452 207.09 112.396C205.933 107.449 205.387 107.677 202.382 98.8492C197.613 84.8463 195.229 77.8473 195.224 77.8089C195.199 77.5046 195.158 76.8901 194.983 76.2685C198.513 75.281 201.94 72.7509 203.13 69.2003C204.083 66.3599 203.832 62.1404 201.516 60.6203C200.021 59.6396 197.713 60.0913 195.568 60.9055C197.245 59.7893 198.735 58.3331 199.028 56.5509C199.682 52.5695 194.332 47.1358 189.479 47.0587C186.09 47.0048 183.053 49.5657 182.946 49.3815C182.833 49.1863 187.067 47.7191 187.723 44.8469C188.541 41.2689 183.687 36.0274 180.284 36.5431C178.032 36.8843 176.567 39.724 176.278 39.5111C175.969 39.2833 178.6 36.7349 178.049 34.3943C177.379 31.5509 172.141 29.5564 168.296 30.7901C167.526 31.037 166.249 31.447 165.604 32.6088C164.337 34.8907 166.541 38.2213 167.509 39.6852C170.589 44.3395 175.018 46.7314 174.823 47.0288C174.642 47.3036 171.226 44.6987 169.769 45.7383C168.857 46.3893 169.109 48.187 169.187 48.7454C170.047 54.8852 177.401 56.8384 182.499 64.5818C186.65 70.8874 184.879 74.3097 188.396 76.0188C189.004 76.3146 189.681 76.5115 190.4 76.6218L190.4 76.6252C190.709 81.4396 199.382 94.3781 204.209 112.774C206.614 121.939 207.929 126.939 206.982 133.901C206.047 140.804 203.449 145.463 203.497 148.538C203.5 148.826 203.525 149.091 203.578 149.348L217.834 147.477C217.93 147.201 218.005 146.917 218.064 146.626C219.15 141.389 215.189 133.43 217.202 120.593C218.696 111.068 222.792 103.233 224.701 99.5828C227.047 95.0939 229.819 90.8715 232.812 84.3147C234.733 85.373 236.912 85.9349 239.074 85.663C242.047 85.2894 245.74 83.2341 246.105 80.4872C246.34 78.7149 244.932 76.8308 243.269 75.2514C245.001 76.278 246.96 76.9897 248.693 76.4806C252.564 75.3432 255.139 68.1655 253.104 63.7593C251.683 60.6819 248.059 59.056 248.178 58.8798ZM190.181 63.5734C190.171 63.5528 190.32 63.488 190.579 63.3828C190.335 63.52 190.192 63.5941 190.181 63.5734ZM237.57 35.8565C237.341 35.6958 237.212 35.5992 237.226 35.5808C237.24 35.5624 237.363 35.6684 237.57 35.8565ZM222.787 43.2052C223.042 43.0898 223.191 43.0266 223.199 43.0484C223.207 43.0702 223.055 43.1227 222.787 43.2052ZM211.071 51.5511C211.06 51.5305 211.209 51.4657 211.468 51.3604C211.224 51.4976 211.081 51.5718 211.071 51.5511ZM215.171 88.3785C214.829 88.3013 218.992 71.4615 219.675 62.5211C221.651 61.2257 223.274 59.4017 224.019 57.178C224.032 57.1394 224.044 57.0995 224.057 57.0604C224.452 57.0056 224.845 56.9312 225.234 56.8395C223.841 60.3624 223.064 62.9205 223.064 62.9205C219.446 74.8395 215.484 88.4498 215.171 88.3785ZM230.709 82.8808C229.567 84.6329 228.228 86.7868 226.384 90.0815C221.656 98.5221 219.293 102.74 218.672 105.747C218.509 106.55 217.648 110.893 215.674 116.398C214.666 119.207 214.161 120.611 213.778 120.554C212.691 120.389 212.673 109.309 215.68 97.8363C216.196 95.8833 216.606 94.6229 217.388 92.2393C218.655 88.351 225.719 66.7384 231.275 53.6271L231.276 53.6253C232.302 52.6417 233.07 51.5165 233.447 50.3321C233.567 49.9539 233.635 49.6041 233.662 49.2712C234.991 49.6937 236.385 49.8639 237.772 49.6895C238.548 49.5919 239.373 49.3789 240.175 49.0698C237.956 53.0021 237.992 58.8909 234.288 65.0691C230.407 71.5443 226.556 71.4324 226.541 75.3428C226.531 77.9397 228.221 80.8124 230.711 82.8782L230.709 82.8808ZM238.529 71.5544C238.543 71.536 238.666 71.642 238.873 71.83C238.643 71.6694 238.515 71.5727 238.529 71.5544Z"
          fill="#F2F2F2"
        />
        <path
          d="M89.4534 54.7155C91.63 54.3528 93.9986 57.0112 94.9879 58.1215C97.629 61.0858 98.1804 64.5343 98.3937 65.9978C101.759 89.0967 103.442 100.646 96.4779 106.018C89.8597 111.123 77.7624 109.754 75.4045 105.167C74.486 103.379 75.2539 101.51 76.4688 97.9289C78.714 91.3105 80.0375 84.4126 82.0032 77.7059C86.4917 62.392 86.1149 55.2719 89.4534 54.7155Z"
          fill="#2F2E41"
        />
        <path
          d="M30.0647 140.929L19.473 160.921C16.7692 166.025 3.9752 244.639 9.66478 245.63L34.3098 246.448L44.5354 246.984L55.0459 247.136L81.0987 248L66.4643 145.613L30.0647 140.929Z"
          fill="#2F2E41"
        />
        <path
          d="M56.1403 82.2827L77.2137 83.1341L78.9167 90.159L83.9915 94.2992C85.8574 95.8214 86.7727 98.2214 86.3943 100.6L84.0254 115.491C84.0254 115.491 88.4955 126.135 77.2137 132.947L65.932 151.254L26.7653 147.209C26.7653 147.209 23.1466 146.783 25.7009 144.229C28.2553 141.674 31.4482 129.115 31.4482 129.115C31.4482 129.115 31.0225 123.58 32.5126 122.09C34.0026 120.6 32.9651 116.768 32.9651 116.768L39.0259 94.3636C40.2069 89.9978 44.1678 86.9659 48.6903 86.9659H55.4112L56.1403 82.2827Z"
          fill="#6D11D2"
        />
        <path
          d="M218.252 225.539L34.2103 176.35C30.2783 175.298 27.5383 172.135 27.0596 168.093C26.5808 164.051 28.506 160.335 32.0836 158.394L106.877 117.829C108.767 116.803 110.977 116.431 113.099 116.781L236.723 137.162C239.362 137.597 241.649 139.052 243.162 141.257C244.676 143.462 245.211 146.119 244.668 148.739L230.293 218.112C229.744 220.76 228.174 223 225.872 224.42C224.312 225.383 222.566 225.875 220.789 225.875C219.945 225.875 219.093 225.764 218.252 225.539Z"
          fill="white"
        />
        <path
          d="M218.252 225.539L34.2103 176.35C30.2783 175.298 27.5383 172.135 27.0596 168.093C26.5808 164.051 28.506 160.335 32.0836 158.394L106.877 117.829C108.767 116.803 110.977 116.431 113.099 116.781L236.723 137.162C239.362 137.597 241.649 139.052 243.162 141.257C244.676 143.462 245.211 146.119 244.668 148.739L230.293 218.112C229.744 220.76 228.174 223 225.872 224.42C224.312 225.383 222.566 225.875 220.789 225.875C219.945 225.875 219.093 225.764 218.252 225.539ZM236.557 138.165L112.934 117.783C111.033 117.47 109.054 117.803 107.361 118.721L32.5679 159.287C29.3636 161.025 27.6393 164.353 28.0681 167.973C28.4969 171.593 30.9509 174.427 34.4724 175.368L218.514 224.558C220.854 225.182 223.278 224.827 225.339 223.555C227.401 222.284 228.807 220.278 229.298 217.906L243.674 148.532C244.16 146.187 243.681 143.807 242.325 141.832C240.969 139.856 238.921 138.554 236.557 138.165Z"
          fill="#CCCCCC"
        />
        <path
          d="M209.82 157.262L193.407 198.68C191.816 202.695 187.349 204.75 183.265 203.344L62.6275 161.835C55.9664 159.543 55.2222 150.419 61.4239 147.078L100.247 126.161C101.936 125.251 103.894 124.971 105.77 125.373L203.997 146.382C208.885 147.427 211.661 152.615 209.82 157.262Z"
          fill="#F2F2F2"
        />
        <path
          d="M178.217 164.133C181.979 164.133 185.029 162.608 185.029 160.727C185.029 158.845 181.979 157.321 178.217 157.321C174.455 157.321 171.406 158.845 171.406 160.727C171.406 162.608 174.455 164.133 178.217 164.133Z"
          fill="#FF6884"
        />
        <path
          d="M171.831 174.563C175.593 174.563 178.643 173.038 178.643 171.157C178.643 169.276 175.593 167.751 171.831 167.751C168.069 167.751 165.02 169.276 165.02 171.157C165.02 173.038 168.069 174.563 171.831 174.563Z"
          fill="#3F3D56"
        />
        <path
          d="M135.219 166.368C145.447 166.368 153.738 162.222 153.738 157.108C153.738 151.994 145.447 147.848 135.219 147.848C124.991 147.848 116.7 151.994 116.7 157.108C116.7 162.222 124.991 166.368 135.219 166.368Z"
          fill="#6D11D2"
        />
        <path
          d="M103.077 156.682C103.339 156.682 103.586 156.519 103.679 156.257C103.796 155.924 103.622 155.559 103.29 155.441L89.7765 150.652C89.4449 150.534 89.0793 150.708 88.9612 151.04C88.8436 151.373 89.0175 151.738 89.3499 151.856L102.863 156.645C102.934 156.67 103.006 156.682 103.077 156.682Z"
          fill="#3F3D56"
        />
        <path
          d="M106.057 154.341C106.32 154.341 106.566 154.177 106.659 153.915C106.776 153.583 106.602 153.218 106.27 153.1L92.7566 148.31C92.425 148.193 92.0594 148.366 91.9413 148.699C91.8237 149.031 91.9977 149.396 92.33 149.514L105.844 154.304C105.914 154.329 105.986 154.341 106.057 154.341Z"
          fill="#3F3D56"
        />
        <path
          d="M109.037 151.999C109.3 151.999 109.546 151.835 109.639 151.573C109.756 151.241 109.582 150.876 109.25 150.758L95.7366 145.968C95.4048 145.851 95.0392 146.025 94.9213 146.357C94.8036 146.69 94.9776 147.055 95.31 147.173L108.823 151.962C108.894 151.987 108.966 151.999 109.037 151.999Z"
          fill="#3F3D56"
        />
        <path
          d="M112.017 149.657C112.28 149.657 112.526 149.494 112.619 149.232C112.737 148.899 112.563 148.534 112.23 148.416L98.7167 143.627C98.3851 143.509 98.0193 143.683 97.9014 144.016C97.7837 144.348 97.9577 144.713 98.2901 144.831L111.804 149.621C111.874 149.645 111.946 149.657 112.017 149.657Z"
          fill="#3F3D56"
        />
        <path
          d="M114.997 147.316C115.26 147.316 115.506 147.152 115.599 146.89C115.717 146.558 115.543 146.193 115.21 146.075L101.697 141.285C101.365 141.168 101 141.341 100.882 141.674C100.764 142.006 100.938 142.371 101.27 142.489L114.784 147.279C114.854 147.304 114.926 147.316 114.997 147.316Z"
          fill="#3F3D56"
        />
        <path
          d="M117.977 144.974C118.24 144.974 118.486 144.811 118.579 144.549C118.697 144.216 118.523 143.851 118.19 143.733L104.677 138.944C104.345 138.826 103.98 139 103.862 139.332C103.744 139.665 103.918 140.03 104.25 140.148L117.764 144.937C117.834 144.962 117.906 144.974 117.977 144.974Z"
          fill="#3F3D56"
        />
        <path
          d="M120.957 142.633C121.22 142.633 121.466 142.469 121.559 142.207C121.677 141.875 121.503 141.51 121.17 141.392L107.657 136.602C107.325 136.485 106.96 136.658 106.842 136.991C106.724 137.323 106.898 137.688 107.23 137.806L120.744 142.596C120.814 142.621 120.886 142.633 120.957 142.633Z"
          fill="#3F3D56"
        />
        <path
          d="M75.4045 89.201C85.0445 89.201 92.8592 81.3859 92.8592 71.7454C92.8592 62.1049 85.0445 54.2898 75.4045 54.2898C65.7645 54.2898 57.9497 62.1049 57.9497 71.7454C57.9497 81.3859 65.7645 89.201 75.4045 89.201Z"
          fill="#A0616A"
        />
        <path
          d="M79.0231 45.7748C74.1087 44.9737 71.4697 44.5435 68.8057 45.1362C63.4691 46.3235 60.3735 51.0948 58.1626 54.5026C52.8754 62.652 55.2658 66.077 50.0738 77.493C46.3492 85.6824 44.4715 85.3439 44.1136 89.6268C43.3625 98.6169 51.0097 107.61 59.2269 111.127C59.7757 111.362 68.3153 114.936 70.7215 111.979C72.4854 109.811 69.3694 106.08 65.6128 96.013C59.0072 78.3125 62.1285 72.3967 63.0584 70.8939C63.6459 69.9445 65.3217 67.5846 66.2513 67.9137C67.0925 68.2114 66.3644 70.3712 67.1028 72.1711C69.0812 76.9934 81.1063 78.0642 90.5177 74.2999C93.2931 73.1898 94.9962 72.5086 96.265 70.681C100.57 64.48 95.4307 51.485 87.3248 47.6907C85.4807 46.8275 83.3282 46.4766 79.0231 45.7748Z"
          fill="#2F2E41"
        />
        <path
          d="M66.5432 149.776C70.7884 151.929 73.1184 155.865 71.7477 158.567C70.3769 161.268 65.8249 161.712 61.5785 159.557C59.8704 158.72 58.3877 157.486 57.2544 155.958L39.3826 146.607L43.9493 138.313L61.0829 148.412C62.9858 148.424 64.8581 148.892 66.5432 149.776Z"
          fill="#A0616A"
        />
        <path
          d="M150.129 137.744C154.601 139.375 157.384 143.005 156.346 145.851C155.307 148.697 150.841 149.681 146.368 148.048C144.572 147.421 142.952 146.372 141.645 144.991L122.785 137.84L126.328 129.061L144.545 137.042C146.436 136.827 148.351 137.067 150.129 137.744Z"
          fill="#A0616A"
        />
        <path
          d="M46.2423 91.3298C46.2423 91.3298 42.6236 83.8792 34.3219 89.6268C26.0203 95.3744 6.22405 119.216 6.22405 119.216C6.22405 119.216 -3.99336 128.157 1.75393 131.35C7.50123 134.543 48.7966 153.063 48.7966 153.063L59.2269 145.825L22.1888 126.88L43.4642 110.914L46.2423 91.3298Z"
          fill="#6D11D2"
        />
        <path
          d="M82.4289 95.8001C82.4289 95.8001 84.9615 92.1813 89.4425 95.8001C93.9235 99.419 106.82 120.256 106.82 120.256L138.625 133.266L133.942 142.845L95.8393 129.221L79.236 106.657L82.4289 95.8001Z"
          fill="#6D11D2"
        />
        <path
          d="M80.4068 89.3075C75.5795 91.7725 77.8007 95.5572 77.6396 96.3323C76.0707 103.879 81.8057 111.696 84.0254 111.233C85.3277 110.962 86.0122 107.719 85.7283 105.273C85.3604 102.102 88.4094 97.0837 88.0698 94.8422C87.2368 89.343 92.2632 89.24 91.2628 87.1787C90.6765 85.9707 83.2835 87.8384 80.4068 89.3075Z"
          fill="#2F2E41"
        />
        <path
          d="M136.363 166.857C136.383 166.857 136.403 166.856 136.424 166.854C136.702 166.821 136.901 166.568 136.868 166.29L135.723 156.622C135.707 156.488 135.639 156.366 135.533 156.283C135.427 156.2 135.293 156.162 135.158 156.178L116.975 158.365C116.697 158.398 116.498 158.651 116.532 158.93C116.565 159.208 116.818 159.408 117.097 159.373L134.774 157.247L135.86 166.409C135.89 166.668 136.109 166.857 136.363 166.857Z"
          fill="white"
        />
        <path
          d="M42.7306 176.195C47.9659 176.195 52.21 171.95 52.21 166.715C52.21 161.479 47.9659 157.235 42.7306 157.235C37.4953 157.235 33.2512 161.479 33.2512 166.715C33.2512 171.95 37.4953 176.195 42.7306 176.195Z"
          fill="#6D11D2"
        />
        <path
          d="M38.4237 165.236L41.4705 170.595C41.6518 170.913 42.1724 170.937 42.3475 170.595C43.5374 168.271 45.2181 166.213 47.2897 164.619C47.5087 164.451 47.6198 164.184 47.4719 163.924C47.3494 163.709 46.9941 163.575 46.7771 163.742C44.5644 165.445 42.7447 167.594 41.4705 170.082H42.3475L39.3007 164.723C38.9776 164.155 38.0998 164.666 38.4237 165.236Z"
          fill="white"
        />
      </g>
      <defs>
        <clipPath id="clip0_228_404">
          <rect width="261" height="248" fill="white" />
        </clipPath>
      </defs>
    </svg>
  );
};

export const CloseModalIcon = ({
  size = 42,
  stroke = "#fff",
  fill = "#4d4a48",
}) => {
  return (
    <svg
      width={size}
      height={size}
      viewBox="0 0 42 42"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <rect width="42" height="42" rx="21" fill={fill} fillOpacity="1" />
      <path
        d="M28.0005 14L14.0005 28M14.0005 14L28.0005 28"
        stroke={stroke}
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  );
};

export const ShareIcon = ({ size = 26, stroke = "#2D394A" }) => {
  return (
    <svg
      width={size}
      height={size}
      viewBox="0 0 26 26"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M22.75 7.04199C22.75 8.83692 21.2949 10.292 19.5 10.292C17.7051 10.292 16.25 8.83692 16.25 7.04199C16.25 5.24707 17.7051 3.79199 19.5 3.79199C21.2949 3.79199 22.75 5.24707 22.75 7.04199Z"
        stroke={stroke}
        strokeWidth="1.5"
      />
      <path
        d="M9.75 13C9.75 14.7949 8.29493 16.25 6.5 16.25C4.70507 16.25 3.25 14.7949 3.25 13C3.25 11.2051 4.70507 9.75 6.5 9.75C8.29493 9.75 9.75 11.2051 9.75 13Z"
        stroke={stroke}
        strokeWidth="1.5"
      />
      <path
        d="M22.75 18.959C22.75 20.7539 21.2949 22.209 19.5 22.209C17.7051 22.209 16.25 20.7539 16.25 18.959C16.25 17.1641 17.7051 15.709 19.5 15.709C21.2949 15.709 22.75 17.1641 22.75 18.959Z"
        stroke={stroke}
        strokeWidth="1.5"
      />
      <path
        d="M9.45581 11.6459L16.4975 8.39648M9.45581 14.3548L16.4975 17.6042"
        stroke={stroke}
        strokeWidth="1.5"
      />
    </svg>
  );
};

export const ReportPostIcon = ({ size = 24, stroke = "#2D394A" }) => {
  return (
    <svg
      width={size}
      height={size}
      viewBox="0 0 24 24"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <circle
        cx="11.9998"
        cy="11.9993"
        r="10.8333"
        stroke={stroke}
        strokeWidth="1.625"
      />
      <path
        d="M11.9912 15.249H12.001"
        stroke={stroke}
        strokeWidth="2.16667"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M11.9998 11.999L11.9998 7.66569"
        stroke={stroke}
        strokeWidth="1.625"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  );
};

export const LocationIcon = ({ size = 15, stroke = "#787E89" }) => {
  return (
    <svg
      width={size}
      height={size}
      viewBox="0 0 15 15"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M8.51109 13.3544C8.24003 13.6081 7.87774 13.75 7.5007 13.75C7.12365 13.75 6.76136 13.6081 6.49031 13.3544C4.00814 11.0162 0.681727 8.40432 2.30392 4.61229C3.18102 2.56197 5.28646 1.25 7.5007 1.25C9.71493 1.25 11.8204 2.56197 12.6975 4.61229C14.3176 8.39954 10.9994 11.0243 8.51109 13.3544Z"
        stroke={stroke}
        strokeWidth="0.9375"
      />
      <path
        d="M9.6875 6.875C9.6875 8.08312 8.70812 9.0625 7.5 9.0625C6.29188 9.0625 5.3125 8.08312 5.3125 6.875C5.3125 5.66688 6.29188 4.6875 7.5 4.6875C8.70812 4.6875 9.6875 5.66688 9.6875 6.875Z"
        stroke={stroke}
        strokeWidth="0.9375"
      />
    </svg>
  );
};

export const LeftArrowBackIcon = ({
  width = 18,
  height = 12,
  stroke = "#2D394A",
}) => {
  return (
    <svg
      width={width}
      height={height}
      viewBox="0 0 18 12"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M2 6L17 5.99976"
        stroke={stroke}
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M6 0.999878L1.70711 5.29277C1.37377 5.6261 1.20711 5.79277 1.20711 5.99988C1.20711 6.20698 1.37377 6.37365 1.70711 6.70698L6 10.9999"
        stroke={stroke}
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  );
};

export const StoriesIcon = ({ size = 32, fill = "#fff" }) => {
  return (
    <svg
      width={size}
      height={size}
      viewBox="0 0 30 32"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <g clipPath="url(#clip0_3443_16346)">
        <path
          d="M25.4401 8.59167C26.0051 9.97377 26.6901 11.5124 27.035 12.8847C27.3445 14.1019 27.4714 15.3582 27.4117 16.6127C27.3533 17.8658 27.1059 19.1028 26.6778 20.282C26.255 21.4443 25.6578 22.5355 24.9068 23.5183C24.1585 24.4899 23.2659 25.3413 22.26 26.0427C21.8863 26.307 21.4993 26.5519 21.1005 26.7766C20.7026 27.0016 20.2949 27.2047 19.8774 27.3857C18.986 27.7818 18.0519 28.074 17.0937 28.2565C16.1534 28.436 15.1951 28.5033 14.239 28.4571L13.943 28.4376L13.2679 30.9522L13.7302 30.9889C14.9493 31.0822 16.1752 31.0238 17.3799 30.8152C18.6023 30.5991 19.794 30.2361 20.9293 29.734C21.4088 29.522 21.8841 29.2831 22.3554 29.0173C22.8129 28.758 23.2727 28.4669 23.7302 28.1489C26.2038 26.4274 28.0831 23.9815 29.1093 21.1479C30.1405 18.291 30.2855 15.1894 29.5252 12.2487C29.036 10.8373 28.5027 8.97328 27.4924 7.54715C26.8246 6.60293 24.8677 7.1631 25.4499 8.59167H25.4401ZM13.6984 11.916C13.6908 11.7399 13.7189 11.564 13.781 11.3991C13.8432 11.2341 13.9381 11.0834 14.06 10.9561C14.1819 10.8287 14.3284 10.7274 14.4905 10.6582C14.6526 10.5889 14.8271 10.5533 15.0034 10.5533C15.1797 10.5533 15.3542 10.5889 15.5163 10.6582C15.6785 10.7274 15.8249 10.8287 15.9469 10.9561C16.0688 11.0834 16.1637 11.2341 16.2258 11.3991C16.288 11.564 16.3161 11.7399 16.3085 11.916V14.6949H19.08C19.4264 14.6949 19.7587 14.8325 20.0037 15.0775C20.2486 15.3225 20.3862 15.6547 20.3862 16.0012C20.3862 16.3476 20.2486 16.6799 20.0037 16.9248C19.7587 17.1698 19.4264 17.3074 19.08 17.3074H16.306V20.0863C16.3136 20.2624 16.2855 20.4383 16.2234 20.6033C16.1612 20.7682 16.0663 20.9189 15.9444 21.0463C15.8225 21.1736 15.676 21.2749 15.5139 21.3442C15.3517 21.4134 15.1773 21.4491 15.001 21.4491C14.8247 21.4491 14.6502 21.4134 14.4881 21.3442C14.3259 21.2749 14.1795 21.1736 14.0575 21.0463C13.9356 20.9189 13.8407 20.7682 13.7786 20.6033C13.7164 20.4383 13.6883 20.2624 13.6959 20.0863V17.3074H10.9146C10.5682 17.3074 10.2359 17.1698 9.99096 16.9248C9.74599 16.6799 9.60836 16.3476 9.60836 16.0012C9.60836 15.6547 9.74599 15.3225 9.99096 15.0775C10.2359 14.8325 10.5682 14.6949 10.9146 14.6949H13.6984V11.916ZM5.93175 27.9679C6.79906 28.6419 7.73941 29.2163 8.73508 29.6802L9.14848 29.8735L9.82118 27.3661L9.53987 27.2316C8.81492 26.871 8.12815 26.4383 7.48997 25.94C6.84385 25.4354 6.24974 24.8675 5.71649 24.2448L5.50856 24.0002L3.26541 25.2942L3.55406 25.644C4.25826 26.5027 5.05572 27.2805 5.93175 27.963V27.9679ZM0.139187 17.9801C0.295258 19.0801 0.576772 20.1585 0.978228 21.1944L1.13723 21.6152L3.38038 20.3211L3.27275 20.0178C2.99691 19.2472 2.8002 18.4505 2.68566 17.6401C2.57167 16.83 2.54134 16.0103 2.59516 15.1939L2.61228 14.8784L0.100048 14.2179L0.0584632 14.6704C-0.0420634 15.7731 -0.0149761 16.8837 0.139187 17.9801ZM2.04966 8.55498C1.82461 8.93414 1.62157 9.31574 1.43566 9.70469L1.23752 10.1205L3.74486 10.7932L3.88674 10.5119C4.02862 10.2355 4.17784 9.96643 4.33439 9.70713C4.49095 9.44784 4.66463 9.1861 4.84809 8.93169C5.14536 8.51373 5.46378 8.11122 5.8021 7.72573C6.13043 7.34819 6.47998 6.98965 6.84907 6.65185L7.07656 6.44393L5.77764 4.19099L5.42539 4.49676C4.92982 4.92863 4.46092 5.39017 4.02128 5.87886C3.57205 6.37729 3.15145 6.9008 2.7615 7.44686C2.50465 7.8089 2.27226 8.18072 2.04721 8.54764L2.04966 8.55498ZM12.3383 1.16262C11.4893 1.30485 10.6561 1.52932 9.85054 1.83287L9.4249 1.99187L10.7214 4.23747L11.0198 4.13228C12.2546 3.70452 13.5564 3.50315 14.8628 3.53786C15.1588 3.53786 15.4694 3.56477 15.7899 3.60146C16.1103 3.63815 16.4039 3.68218 16.7047 3.74334C17.0056 3.80449 17.3285 3.88033 17.6343 3.97083C17.9401 4.06134 18.2458 4.16653 18.5614 4.28884C18.7188 4.35189 18.8871 4.3831 19.0566 4.38067C19.2261 4.37823 19.3934 4.3422 19.5489 4.27465C19.7044 4.20711 19.8449 4.10939 19.9624 3.98717C20.0798 3.86494 20.1719 3.72062 20.2332 3.56258C20.2945 3.40453 20.3238 3.2359 20.3195 3.06643C20.3152 2.89697 20.2773 2.73004 20.2081 2.57531C20.1388 2.42059 20.0396 2.28114 19.9161 2.16503C19.7925 2.04893 19.6472 1.95848 19.4885 1.89892C19.1167 1.75459 18.7351 1.62494 18.3461 1.50997C17.9572 1.395 17.5829 1.30449 17.2087 1.22866C16.8344 1.15283 16.4577 1.09412 16.0859 1.05254C15.714 1.01095 15.3202 0.984045 14.9215 0.971814C14.0563 0.953491 13.1914 1.01738 12.3383 1.16262Z"
          fill={fill}
        />
      </g>
      <defs>
        <clipPath id="clip0_3443_16346">
          <rect
            width="30"
            height="30.0587"
            fill="white"
            transform="translate(1.52588e-05 0.970703)"
          />
        </clipPath>
      </defs>
    </svg>
  );
};

export const ProfileIcon = ({ size = 20, stroke = "#6D11D2" }) => {
  return (
    <svg
      width={size}
      height={size}
      viewBox="0 0 18 20"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M4.28476 13.0286C3.05453 13.7611 -0.171072 15.2569 1.79353 17.1286C2.75323 18.0429 3.82208 18.6968 5.16589 18.6968H12.8339C14.1778 18.6968 15.2466 18.0429 16.2063 17.1286C18.1709 15.2569 14.9453 13.7611 13.7151 13.0286C10.8302 11.3108 7.16964 11.3108 4.28476 13.0286Z"
        stroke={stroke}
        strokeWidth="1.30435"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M12.912 5.21773C12.912 7.37885 11.1601 9.13078 8.99898 9.13078C6.83787 9.13078 5.08594 7.37885 5.08594 5.21773C5.08594 3.05662 6.83787 1.30469 8.99898 1.30469C11.1601 1.30469 12.912 3.05662 12.912 5.21773Z"
        stroke={stroke}
        strokeWidth="1.30435"
      />
    </svg>
  );
};

export const ChangePasswordIcon = ({ size = 20, stroke = "#6D11D2" }) => {
  return (
    <svg
      width={size}
      height={size}
      viewBox="0 0 16 20"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M1.27585 15.9513C1.47139 17.4037 2.67439 18.5415 4.13832 18.6088C5.37016 18.6654 6.62149 18.695 7.99949 18.695C9.37749 18.695 10.6288 18.6654 11.8607 18.6088C13.3246 18.5415 14.5276 17.4037 14.7231 15.9513C14.8508 15.0034 14.956 14.032 14.956 13.0428C14.956 12.0536 14.8508 11.0822 14.7231 10.1343C14.5276 8.68195 13.3246 7.54409 11.8607 7.47679C10.6288 7.42016 9.37749 7.39062 7.99949 7.39062C6.62149 7.39062 5.37016 7.42016 4.13832 7.47679C2.67438 7.54409 1.47139 8.68195 1.27585 10.1343C1.14822 11.0822 1.04297 12.0536 1.04297 13.0428C1.04297 14.032 1.14822 15.0034 1.27585 15.9513Z"
        stroke={stroke}
        strokeWidth="1.46341"
      />
      <path
        d="M4.08789 7.39164V5.21773C4.08789 3.05662 5.83982 1.30469 8.00093 1.30469C10.162 1.30469 11.914 3.05662 11.914 5.21773V7.39164"
        stroke={stroke}
        strokeWidth="1.46341"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M7.99609 13.043H8.0039"
        stroke={stroke}
        strokeWidth="1.95122"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  );
};
export const CustomizeHeaderIcon = ({ size = 20, stroke = "#6D11D2" }) => {
  return (
    <svg
      width={size}
      height={size}
      viewBox="0 0 18 18"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M16.8233 13.7798C16.8233 15.4607 15.4607 16.8233 13.7798 16.8233C12.0989 16.8233 10.7363 15.4607 10.7363 13.7798C10.7363 12.0989 12.0989 10.7363 13.7798 10.7363C15.4607 10.7363 16.8233 12.0989 16.8233 13.7798Z"
        stroke={stroke}
        strokeWidth="1.30435"
      />
      <path
        d="M13.7806 10.7363H4.21535C2.53449 10.7363 1.17188 12.0989 1.17188 13.7798C1.17188 15.4607 2.53449 16.8233 4.21536 16.8233H13.7806C15.4614 16.8233 16.824 15.4607 16.824 13.7798C16.824 12.0989 15.4614 10.7363 13.7806 10.7363Z"
        stroke={stroke}
        strokeWidth="1.30435"
      />
      <path
        d="M1.17281 4.21535C1.17281 5.89622 2.53542 7.25883 4.21629 7.25883C5.89715 7.25883 7.25977 5.89622 7.25977 4.21535C7.25977 2.53449 5.89715 1.17188 4.21629 1.17188C2.53542 1.17188 1.17281 2.53449 1.17281 4.21535Z"
        stroke={stroke}
        strokeWidth="1.30435"
      />
      <path
        d="M4.21553 1.17188H13.7807C15.4616 1.17188 16.8242 2.53449 16.8242 4.21535C16.8242 5.89622 15.4616 7.25883 13.7807 7.25883H4.21552C2.53466 7.25883 1.17204 5.89622 1.17204 4.21535C1.17204 2.53449 2.53466 1.17188 4.21553 1.17188Z"
        stroke={stroke}
        strokeWidth="1.30435"
      />
    </svg>
  );
};
export const ChangeThemeIcon = ({ size = 20, stroke = "#6D11D2" }) => {
  return (
    <svg
      width={size}
      height={size}
      viewBox="0 0 20 20"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M14.348 10.0002C14.348 12.4014 12.4014 14.348 10.0002 14.348C7.59893 14.348 5.65234 12.4014 5.65234 10.0002C5.65234 7.59893 7.59893 5.65234 10.0002 5.65234C12.4014 5.65234 14.348 7.59893 14.348 10.0002Z"
        stroke={stroke}
        strokeWidth="1.56522"
      />
      <path
        d="M10.0003 1.30469V2.60904M10.0003 17.3916V18.696M16.1489 16.1493L15.2265 15.227M4.77361 4.77361L3.85129 3.85129M18.696 10.0003H17.3916M2.60904 10.0003H1.30469M16.1493 3.85139L15.227 4.77371M4.77403 15.2271L3.85172 16.1494"
        stroke={stroke}
        strokeWidth="1.56522"
        strokeLinecap="round"
      />
    </svg>
  );
};
export const AboutUsIcon = ({ size = 20, stroke = "#6D11D2" }) => {
  return (
    <svg
      width={size}
      height={size}
      viewBox="0 0 20 20"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M18.694 9.99966C18.694 5.19718 14.8009 1.30401 9.99839 1.30401C5.19591 1.30401 1.30273 5.19718 1.30273 9.99966C1.30273 14.8021 5.19591 18.6953 9.99839 18.6953C14.8009 18.6953 18.694 14.8021 18.694 9.99966Z"
        stroke={stroke}
        strokeWidth="1.56522"
      />
      <path
        d="M10.2094 14.3483V10.0004C10.2094 9.59051 10.2094 9.38555 10.0821 9.2582C9.95472 9.13086 9.74976 9.13086 9.33984 9.13086"
        stroke={stroke}
        strokeWidth="1.56522"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M9.99202 6.52148H9.99983"
        stroke={stroke}
        strokeWidth="1.73913"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  );
};
export const TermsIcon = ({ size = 20, stroke = "#6D11D2" }) => {
  return (
    <svg
      width={size}
      height={size}
      viewBox="0 0 18 20"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M12.4785 14.3486L6.39156 14.3486"
        stroke={stroke}
        strokeWidth="1.30435"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M12.4785 10.8701L9.86982 10.8701"
        stroke={stroke}
        strokeWidth="1.30435"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M16.3926 11.7398C16.3926 15.0191 16.3926 16.6588 15.3101 17.6775C14.2277 18.6963 12.4856 18.6963 9.00127 18.6963H8.32934C5.49351 18.6963 4.07559 18.6963 3.0909 18.0025C2.80877 17.8037 2.5583 17.568 2.3471 17.3025C1.60997 16.3757 1.60997 15.0412 1.60997 12.3722V10.1587C1.60997 7.58207 1.60997 6.29373 2.01774 5.26477C2.67329 3.61057 4.05965 2.30575 5.81724 1.68877C6.91052 1.30499 8.27937 1.30499 11.0171 1.30499C12.5815 1.30499 13.3637 1.30499 13.9884 1.52429C14.9928 1.87685 15.785 2.62246 16.1596 3.56772C16.3926 4.1557 16.3926 4.89189 16.3926 6.36427V11.7398Z"
        stroke={stroke}
        strokeWidth="1.30435"
        strokeLinejoin="round"
      />
      <path
        d="M1.60904 10C1.60904 8.39917 2.90676 7.10145 4.50759 7.10145C5.08653 7.10145 5.76907 7.20289 6.33196 7.05207C6.83209 6.91806 7.22274 6.52741 7.35675 6.02727C7.50758 5.46438 7.40614 4.78184 7.40614 4.2029C7.40614 2.60207 8.70386 1.30435 10.3047 1.30435"
        stroke={stroke}
        strokeWidth="1.30435"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  );
};
export const PrivacyPolicyIcon = ({ size = 20, stroke = "#6D11D2" }) => {
  return (
    <svg
      width={size}
      height={size}
      viewBox="0 0 18 20"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M8.99648 1.30469C6.51868 1.30469 4.91207 3.06033 3.01201 3.70025C2.23943 3.96045 1.85313 4.09055 1.6968 4.27395C1.54047 4.45734 1.49469 4.72534 1.40313 5.26133C0.4234 10.9969 2.56483 16.2994 7.67189 18.3634C8.22061 18.5851 8.49498 18.696 8.99918 18.696C9.50339 18.696 9.77774 18.5851 10.3264 18.3633C15.4332 16.2994 17.5726 10.9968 16.5925 5.26132C16.5009 4.72524 16.4551 4.45721 16.2988 4.27381C16.1424 4.09041 15.7561 3.96038 14.9836 3.70033C13.0828 3.06047 11.4744 1.30469 8.99648 1.30469Z"
        stroke={stroke}
        strokeWidth="1.30435"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M15.5468 4.37424C15.8014 4.11955 15.8014 3.70662 15.5468 3.45193C15.2921 3.19724 14.8791 3.19724 14.6244 3.45193L15.5468 4.37424ZM3.32009 14.7563C3.0654 15.011 3.0654 15.4239 3.32009 15.6786C3.57478 15.9333 3.98772 15.9333 4.24241 15.6786L3.32009 14.7563ZM14.6244 3.45193L3.32009 14.7563L4.24241 15.6786L15.5468 4.37424L14.6244 3.45193Z"
        fill={stroke}
      />
    </svg>
  );
};
export const SignOutIcon = ({ size = 20, stroke = "#6D11D2" }) => {
  return (
    <svg
      width={size}
      height={size}
      viewBox="0 0 18 20"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M3.78057 5.21777C2.17949 6.65078 1.17188 8.55207 1.17188 10.8699C1.17188 15.1921 4.67573 18.696 8.99796 18.696C13.3202 18.696 16.824 15.1921 16.824 10.8699C16.824 8.55207 15.8164 6.65078 14.2154 5.21777"
        stroke={stroke}
        strokeWidth="1.30435"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M8.99813 1.30469V9.13077M8.99813 1.30469C8.38924 1.30469 7.25164 3.03886 6.82422 3.4786M8.99813 1.30469C9.60703 1.30469 10.7446 3.03886 11.172 3.4786"
        stroke={stroke}
        strokeWidth="1.30435"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  );
};

export const UploadImageIcon = ({ width = 120, height = 86 }) => {
  return (
    <svg
      width={width}
      height={height}
      viewBox="0 0 120 86"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M0.0110847 53.6117C0.00354864 52.2491 0.00113606 50.8256 0.000363699 49.338L0.690019 49.3376C0.689655 48.6373 0.689655 47.9227 0.689655 47.1933V45.0501H0V40.7638H0.689655V38.6207C0.689655 37.8913 0.689655 37.1767 0.690019 36.4763L0.0003637 36.476C0.00113607 34.9883 0.00354865 33.5649 0.0110847 32.2022L0.700729 32.206C0.709004 30.7098 0.723456 29.2884 0.748681 27.9365L0.0591458 27.9237C0.0873288 26.4132 0.128941 24.9883 0.190381 23.6423L0.879319 23.6737C0.948279 22.163 1.04218 20.7564 1.16987 19.4437L0.483454 19.3769C0.632196 17.8477 0.8267 16.4398 1.08105 15.1394L1.75788 15.2718C2.04695 13.7938 2.41264 12.4693 2.8736 11.2723L2.23002 11.0245C2.76687 9.63041 3.43297 8.39078 4.25942 7.27446L4.8137 7.68482C5.21542 7.1422 5.65721 6.62985 6.14353 6.14353C6.62985 5.65721 7.1422 5.21542 7.68482 4.8137L7.27447 4.25941C8.39078 3.43296 9.63041 2.76687 11.0245 2.23002L11.2723 2.8736C12.4693 2.41264 13.7938 2.04695 15.2718 1.75788L15.1394 1.08104C16.4398 0.826699 17.8477 0.632195 19.3769 0.483454L19.4437 1.16987C20.7564 1.04218 22.163 0.948279 23.6737 0.879319L23.6423 0.190381C24.9883 0.128941 26.4133 0.0873287 27.9237 0.0591458L27.9365 0.748681C29.2884 0.723456 30.7098 0.709004 32.206 0.700729L32.2022 0.0110847C33.5649 0.00354864 34.9883 0.00113606 36.476 0.000363699L36.4763 0.690019C37.1767 0.689655 37.8913 0.689655 38.6207 0.689655H40.7586V0H45.0345V0.689655H49.3103V0H53.5862V0.689655H57.8621V0H62.1379V0.689655H66.4138V0H70.6897V0.689655H74.9655V0H79.2414V0.689655H81.3793C82.1087 0.689655 82.8233 0.689655 83.5237 0.690019L83.524 0.000363698C85.0117 0.00113606 86.4351 0.00354864 87.7978 0.0110846L87.794 0.700729C89.2902 0.709004 90.7116 0.723456 92.0635 0.748681L92.0763 0.0591457C93.5867 0.0873287 95.0117 0.128941 96.3577 0.190381L96.3263 0.879319C97.837 0.948279 99.2436 1.04218 100.556 1.16987L100.623 0.483454C102.152 0.632196 103.56 0.8267 104.861 1.08105L104.728 1.75788C106.206 2.04695 107.531 2.41264 108.728 2.8736L108.976 2.23002C110.37 2.76687 111.609 3.43297 112.726 4.25942L112.315 4.8137C112.858 5.21542 113.37 5.65721 113.856 6.14353C114.343 6.62985 114.785 7.1422 115.186 7.68482L115.741 7.27446C116.567 8.39078 117.233 9.63041 117.77 11.0245L117.126 11.2723C117.587 12.4693 117.953 13.7938 118.242 15.2718L118.919 15.1394C119.173 16.4398 119.368 17.8477 119.517 19.3769L118.83 19.4437C118.958 20.7564 119.052 22.163 119.121 23.6737L119.81 23.6423C119.871 24.9883 119.913 26.4132 119.941 27.9237L119.251 27.9365C119.277 29.2884 119.291 30.7098 119.299 32.206L119.989 32.2022C119.996 33.5649 119.999 34.9883 120 36.476L119.31 36.4763C119.31 37.1767 119.31 37.8913 119.31 38.6207V40.7638H120V45.0501H119.31V47.1933C119.31 47.9227 119.31 48.6373 119.31 49.3376L120 49.338C119.999 50.8257 119.996 52.2491 119.989 53.6117L119.299 53.6079C119.291 55.1041 119.277 56.5256 119.251 57.8774L119.941 57.8903C119.913 59.4007 119.871 60.8257 119.81 62.1717L119.121 62.1402C119.052 63.651 118.958 65.0575 118.83 66.3703L119.517 66.437C119.368 67.9663 119.173 69.3741 118.919 70.6746L118.242 70.5422C117.953 72.0202 117.587 73.3446 117.126 74.5416L117.77 74.7895C117.233 76.1835 116.567 77.4232 115.741 78.5395L115.186 78.1291C114.785 78.6718 114.343 79.1841 113.856 79.6704C113.37 80.1567 112.858 80.5985 112.315 81.0003L112.726 81.5545C111.609 82.381 110.37 83.0471 108.976 83.5839L108.728 82.9403C107.531 83.4013 106.206 83.767 104.728 84.0561L104.861 84.7329C103.56 84.9873 102.152 85.1818 100.623 85.3305L100.556 84.6441C99.2436 84.7718 97.837 84.8657 96.3263 84.9346L96.3577 85.6236C95.0117 85.685 93.5867 85.7266 92.0763 85.7548L92.0635 85.0653C90.7116 85.0905 89.2902 85.1049 87.794 85.1132L87.7978 85.8029C86.4351 85.8104 85.0117 85.8128 83.524 85.8136L83.5237 85.1239C82.8233 85.1243 82.1087 85.1243 81.3793 85.1243H79.2414V85.8139H74.9655V85.1243H70.6897V85.8139H66.4138V85.1243H62.1379V85.8139H57.8621V85.1243H53.5862V85.8139H49.3103V85.1243H45.0345V85.8139H40.7586V85.1243H38.6207C37.8913 85.1243 37.1767 85.1243 36.4763 85.1239L36.476 85.8136C34.9883 85.8128 33.5649 85.8104 32.2022 85.8029L32.206 85.1132C30.7098 85.1049 29.2884 85.0905 27.9365 85.0653L27.9237 85.7548C26.4133 85.7266 24.9883 85.685 23.6423 85.6236L23.6737 84.9346C22.163 84.8657 20.7564 84.7718 19.4437 84.6441L19.3769 85.3305C17.8477 85.1818 16.4398 84.9873 15.1394 84.7329L15.2718 84.0561C13.7938 83.767 12.4693 83.4013 11.2723 82.9404L11.0245 83.5839C9.63041 83.0471 8.39078 82.381 7.27446 81.5545L7.68482 81.0003C7.1422 80.5985 6.62985 80.1567 6.14353 79.6704C5.65721 79.1841 5.21542 78.6718 4.8137 78.1291L4.25942 78.5395C3.43297 77.4232 2.76687 76.1835 2.23002 74.7895L2.8736 74.5416C2.41264 73.3446 2.04695 72.0202 1.75788 70.5422L1.08105 70.6746C0.826699 69.3741 0.632195 67.9663 0.483454 66.437L1.16987 66.3703C1.04218 65.0575 0.948279 63.651 0.879319 62.1402L0.190381 62.1717C0.128941 60.8257 0.0873287 59.4007 0.0591458 57.8903L0.748681 57.8774C0.723456 56.5256 0.709004 55.1041 0.700729 53.6079L0.0110847 53.6117Z"
        fill="#F1F2F3"
        stroke="#787E89"
        strokeWidth="1.37931"
        strokeDasharray="4.14 4.14"
      />
      <circle cx="61.3771" cy="43.4478" r="22.7579" fill="#6D11D2" />
      <path
        d="M62.2859 35.2592C61.8579 35.2571 61.4034 35.2571 60.9203 35.2571C56.8432 35.2571 54.8047 35.2571 53.5381 36.5237C52.2715 37.7902 52.2715 39.8288 52.2715 43.9059C52.2715 47.9829 52.2715 50.0215 53.5381 51.2881C54.8047 52.5546 56.8432 52.5546 60.9203 52.5546C64.9973 52.5546 67.0359 52.5546 68.3025 51.2881C69.521 50.0695 69.5672 48.1365 69.569 44.3611"
        stroke="white"
        strokeWidth="1.37931"
        strokeLinecap="round"
      />
      <path
        d="M52.2715 45.3947C52.835 45.3129 53.4048 45.2724 53.9755 45.2739C56.3898 45.2228 58.745 45.9751 60.6208 47.3965C62.3605 48.7147 63.5829 50.5289 64.1067 52.5546"
        stroke="white"
        strokeWidth="1.37931"
        strokeLinejoin="round"
      />
      <path
        d="M69.5705 47.9081C68.5004 47.3661 67.3936 47.0911 66.2806 47.0923C64.5947 47.0856 62.926 47.7052 61.377 48.913"
        stroke="white"
        strokeWidth="1.37931"
        strokeLinejoin="round"
      />
      <path
        d="M65.9277 36.6227C66.3752 36.1623 67.5662 34.3467 68.2037 34.3467M70.4797 36.6227C70.0322 36.1623 68.8412 34.3467 68.2037 34.3467M68.2037 34.3467V41.6299"
        stroke="white"
        strokeWidth="1.37931"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  );
};

export const CheckMarkIcon = ({
  size = 28,
  // fill = "transparent",
  fill = "#10BE5B",
  stroke = "#fff",
}) => {
  return (
    <svg
      width={size}
      height={size}
      viewBox="0 0 28 28"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <rect width="28" height="28" rx="14" fill={fill} />
      <path
        d="M9.69238 15.167L13.0578 18.308L20.4616 10.7695"
        stroke={stroke}
        strokeWidth="3.23077"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  );
};

export const GenieChatIcon = ({ size = 30, bgFill = "#E8AB2E" }) => {
  return (
    <svg
      width={size}
      height={size}
      viewBox="0 0 30 30"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M1.2931 28.3759C1.10172 28.5931 1.2569 28.9397 1.55172 28.95C3.46552 29.0483 5.39483 28.619 7.12759 27.7707C9.41379 29.1879 12.1138 30 15 30C23.2862 30 30 23.2862 30 15C30 6.71379 23.2862 0 15 0C6.71379 0 0 6.71379 0 15C0 18.5897 1.2569 21.8793 3.36207 24.4603C2.93793 25.9138 2.21897 27.3 1.2931 28.3759Z"
        fill={bgFill}
      />
      <path
        d="M5.86197 8.04199C5.66527 8.04199 5.50586 8.20145 5.50586 8.3981V15.2636C5.50586 15.4603 5.66532 15.6197 5.86197 15.6197C6.05862 15.6197 6.21808 15.4603 6.21808 15.2636V8.3981C6.21813 8.20145 6.05867 8.04199 5.86197 8.04199Z"
        fill="#DEDDE0"
      />
      <path
        d="M7.29378 19.5582H8.00881V13.8379H7.29378C5.71411 13.8379 4.43359 15.1184 4.43359 16.698C4.43359 18.2733 5.70978 19.5582 7.29378 19.5582Z"
        fill="#6D11D2"
      />
      <path
        d="M23.7404 19.5582H23.0254V13.8379H23.7404C25.32 13.8379 26.6006 15.1184 26.6006 16.6981C26.6006 18.2733 25.3244 19.5582 23.7404 19.5582Z"
        fill="#B25EF9"
      />
      <path
        d="M24.4541 16.6917C24.4541 20.4329 21.4228 23.4846 17.6612 23.4846H13.3706C9.61943 23.4847 6.57812 20.4434 6.57812 16.6917C6.57812 11.7691 10.5662 7.75391 15.5159 7.75391C17.485 7.75391 19.3093 8.39207 20.7889 9.47469C23.0088 11.0986 24.4541 13.7221 24.4541 16.6917Z"
        fill="#B25EF9"
      />
      <path
        d="M20.7889 9.47469H20.1013C19.9123 9.47469 19.731 9.54972 19.5975 9.68314L17.8046 11.4766C17.3558 11.9253 16.5885 11.6071 16.5885 10.9727V10.1869C16.5885 9.79324 16.2694 9.4746 15.8762 9.4746H15.1474C14.5296 9.4746 13.9579 9.27753 13.4912 8.94373C13.1094 8.66974 12.5714 8.85068 12.4143 9.29366C11.2424 12.5948 10.5895 18.3142 16.3122 23.4847H13.3706C9.61943 23.4847 6.57812 20.4434 6.57812 16.6917C6.57812 11.7691 10.5662 7.75391 15.5159 7.75391C17.485 7.75391 19.3093 8.39207 20.7889 9.47469Z"
        fill="#6D11D2"
      />
      <path
        d="M22.3105 16.6875C22.3105 19.0503 20.3964 20.9776 18.0204 20.9776H13.0152C10.6453 20.9776 8.72461 19.0569 8.72461 16.6875C8.72461 14.7891 9.96015 13.1724 11.6785 12.6097C12.0988 12.472 12.548 12.3975 13.0152 12.3975H18.0204C20.3898 12.3974 22.3105 14.3181 22.3105 16.6875Z"
        fill="#F4F4F5"
      />
      <path
        d="M14.0394 20.9832H13.0152C10.6453 20.9832 8.72461 19.0625 8.72461 16.6931C8.72461 14.7947 9.96015 13.1779 11.6785 12.6152C11.4506 15.0853 11.8998 18.0459 14.0394 20.9832Z"
        fill="#DEDDE0"
      />
      <path
        d="M5.86359 9.89906C6.45596 9.89906 6.93617 9.41885 6.93617 8.82648C6.93617 8.23411 6.45596 7.75391 5.86359 7.75391C5.27122 7.75391 4.79102 8.23411 4.79102 8.82648C4.79102 9.41885 5.27122 9.89906 5.86359 9.89906Z"
        fill="#6D11D2"
      />
      <path
        d="M16.936 16.6404C16.9355 15.7615 16.2988 15.6793 15.5134 15.6797C14.728 15.6801 14.0914 15.763 14.0918 16.6418C14.0923 17.5207 14.7293 18.2328 15.5147 18.2324C16.3001 18.2321 16.9365 17.5193 16.936 16.6404Z"
        fill="#495560"
      />
      <path
        d="M11.737 16.2625C11.5404 16.2626 11.381 16.1032 11.3809 15.9066C11.3806 15.3282 11.8484 14.8597 12.427 14.8594C13.0036 14.8591 13.4738 15.3279 13.4742 15.9055C13.4743 16.1022 13.3149 16.2617 13.1182 16.2618C12.9218 16.2619 12.762 16.1028 12.7619 15.9059C12.7618 15.607 12.3996 15.4608 12.1909 15.6697C12.1278 15.7329 12.0931 15.8169 12.0931 15.9062C12.0932 16.1024 11.9343 16.2625 11.737 16.2625Z"
        fill="#495560"
      />
      <path
        d="M17.9108 16.2596C17.7142 16.2596 17.5548 16.1003 17.5547 15.9036C17.5544 15.3265 18.0237 14.8568 18.6009 14.8564C19.1792 14.8561 19.6478 15.324 19.648 15.9026C19.6481 16.0993 19.4888 16.2588 19.2921 16.2589C19.096 16.259 18.9359 16.1003 18.9358 15.9029C18.9357 15.7183 18.7862 15.5687 18.6014 15.5687C18.4175 15.5687 18.2669 15.7183 18.267 15.9033C18.267 16.0991 18.1085 16.2596 17.9108 16.2596Z"
        fill="#495560"
      />
    </svg>
  );
};

export const SentChatIcon = ({
  size = 58,
  bgFill = "#6D11D2",
  arrowFill = "#fff",
}) => {
  return (
    <svg
      width={size}
      height={size}
      viewBox="0 0 58 58"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <circle cx="29" cy="29" r="29" fill={bgFill} />
      <path
        d="M37.2646 27.8231L21.4342 20.1009C21.298 20.0345 21.1485 20 20.9971 20C20.4464 20 20 20.4464 20 20.9971V21.0258C20 21.1596 20.0164 21.2929 20.0489 21.4227L21.5674 27.4968C21.6088 27.6627 21.7491 27.7852 21.9191 27.8041L28.5934 28.5457C28.8249 28.5714 29 28.7671 29 29C29 29.2329 28.8249 29.4286 28.5934 29.4543L21.9191 30.1959C21.7491 30.2148 21.6088 30.3373 21.5674 30.5032L20.0489 36.5773C20.0164 36.7071 20 36.8404 20 36.9742V37.003C20 37.5536 20.4464 38 20.9971 38C21.1485 38 21.298 37.9655 21.4342 37.899L37.2646 30.1769C37.7145 29.9574 38 29.5006 38 29C38 28.4994 37.7145 28.0426 37.2646 27.8231Z"
        fill={arrowFill}
      />
    </svg>
  );
};

export const EditProfileIcon = ({ size = 35, stroke = "white" }) => {
  return (
    <svg
      width={size}
      height={size}
      viewBox="0 0 20 19"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <g clipPath="url(#clip0_7226_87588)">
        <path
          d="M11.6031 3.2294C12.1789 2.60552 12.4669 2.29359 12.7728 2.11163C13.511 1.67259 14.42 1.65894 15.1705 2.07562C15.4816 2.24831 15.7783 2.55146 16.3719 3.15778C16.9654 3.76409 17.2622 4.06725 17.4312 4.38499C17.8391 5.15169 17.8257 6.08025 17.396 6.83437C17.2178 7.1469 16.9125 7.44101 16.3018 8.02924L9.03529 15.0281C7.87793 16.1428 7.29926 16.7001 6.57603 16.9826C5.8528 17.2651 5.05773 17.2443 3.46758 17.2027L3.25123 17.1971C2.76714 17.1844 2.52509 17.1781 2.38439 17.0184C2.24369 16.8587 2.2629 16.6122 2.30132 16.119L2.32218 15.8513C2.43031 14.4634 2.48437 13.7694 2.75539 13.1456C3.02641 12.5218 3.49391 12.0153 4.4289 11.0023L11.6031 3.2294Z"
          stroke={stroke}
          strokeWidth="2"
          strokeLinejoin="round"
        />
        <path
          d="M10.7734 3.31836L16.1825 8.72745"
          stroke={stroke}
          strokeWidth="2"
          strokeLinejoin="round"
        />
      </g>
      <defs>
        <clipPath id="clip0_7226_87588">
          <rect
            width="18.5455"
            height="18.5455"
            fill="white"
            transform="translate(0.726562 0.227539)"
          />
        </clipPath>
      </defs>
    </svg>
  );
};

export const MovePostIcon = ({ size = 24, stroke = "#141B34" }) => {
  return (
    <svg
      width={size}
      height={size}
      viewBox="0 0 24 24"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M2.5 12C2.5 7.52166 2.5 5.28249 3.89124 3.89124C5.28249 2.5 7.52166 2.5 12 2.5C16.4783 2.5 18.7175 2.5 20.1088 3.89124C21.5 5.28249 21.5 7.52166 21.5 12C21.5 16.4783 21.5 18.7175 20.1088 20.1088C18.7175 21.5 16.4783 21.5 12 21.5C7.52166 21.5 5.28249 21.5 3.89124 20.1088C2.5 18.7175 2.5 16.4783 2.5 12Z"
        stroke={stroke}
        strokeWidth="1.5"
      />
      <path
        d="M7.25 17C7.25 17.4142 7.58579 17.75 8 17.75C8.41421 17.75 8.75 17.4142 8.75 17H7.25ZM16 9.91027C16.4142 9.91027 16.75 9.57448 16.75 9.16027C16.75 8.74605 16.4142 8.41027 16 8.41027V9.91027ZM13.3016 9.23451L13.4143 9.976L13.3016 9.23451ZM8.07738 14.2474L7.33739 14.1253L8.07738 14.2474ZM14.692 6.46477C14.3965 6.17461 13.9216 6.17901 13.6314 6.47461C13.3413 6.77021 13.3457 7.24506 13.6413 7.53523L14.692 6.46477ZM15.7401 8.54452L16.2655 8.00929V8.00929L15.7401 8.54452ZM15.7401 9.77641L16.2655 10.3116L15.7401 9.77641ZM13.6413 10.7857C13.3457 11.0759 13.3413 11.5507 13.6314 11.8463C13.9216 12.1419 14.3965 12.1463 14.692 11.8562L13.6413 10.7857ZM8.75 17V16.3969H7.25V17H8.75ZM16 8.41027C14.8707 8.41027 13.7496 8.40782 13.1889 8.49303L13.4143 9.976C13.8307 9.91271 14.7938 9.91027 16 9.91027V8.41027ZM8.75 16.3969C8.75 15.2466 8.75252 14.7624 8.81736 14.3696L7.33739 14.1253C7.24748 14.67 7.25 15.3063 7.25 16.3969H8.75ZM13.1889 8.49303C10.1962 8.94786 7.81906 11.2071 7.33739 14.1253L8.81736 14.3696C9.18755 12.1269 11.0287 10.3386 13.4143 9.976L13.1889 8.49303ZM13.6413 7.53523L15.2147 9.07974L16.2655 8.00929L14.692 6.46477L13.6413 7.53523ZM15.2147 9.24119L13.6413 10.7857L14.692 11.8562L16.2655 10.3116L15.2147 9.24119ZM15.2147 9.07974C15.2618 9.12594 15.2618 9.19499 15.2147 9.24119L16.2655 10.3116C16.9115 9.67748 16.9115 8.64345 16.2655 8.00929L15.2147 9.07974Z"
        fill={stroke}
      />
    </svg>
  );
};

export const PinIcon = ({ size = 24, stroke = "#2D394A" }) => {
  return (
    <svg
      width={size}
      height={size}
      viewBox="0 0 24 24"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M3 21L8 16"
        stroke={stroke}
        strokeWidth="2"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M13.2585 18.8714C9.51516 18.0215 5.97844 14.4848 5.12853 10.7415C4.99399 10.1489 4.92672 9.85266 5.12161 9.37197C5.3165 8.89129 5.55457 8.74255 6.03071 8.44509C7.10705 7.77265 8.27254 7.55888 9.48209 7.66586C11.1793 7.81598 12.0279 7.89104 12.4512 7.67048C12.8746 7.44991 13.1622 6.93417 13.7376 5.90269L14.4664 4.59604C14.9465 3.73528 15.1866 3.3049 15.7513 3.10202C16.316 2.89913 16.6558 3.02199 17.3355 3.26771C18.9249 3.84236 20.1576 5.07505 20.7323 6.66449C20.978 7.34417 21.1009 7.68401 20.898 8.2487C20.6951 8.8134 20.2647 9.05346 19.4039 9.53358L18.0672 10.2792C17.0376 10.8534 16.5229 11.1406 16.3024 11.568C16.0819 11.9955 16.162 12.8256 16.3221 14.4859C16.4399 15.7068 16.2369 16.88 15.5555 17.9697C15.2577 18.4458 15.1088 18.6839 14.6283 18.8786C14.1477 19.0733 13.8513 19.006 13.2585 18.8714Z"
        stroke={stroke}
        strokeWidth="2"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  );
};

export const ProjectImageUploadIcon = ({ width = 430, height = 271 }) => {
  return (
    <svg
      width={width}
      height={height}
      viewBox="0 0 400 271"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <rect x="0.5" y="0.5" width="399" height="270" rx="13.5" fill="#F1F2F3" />
      <rect
        x="0.5"
        y="0.5"
        width="399"
        height="270"
        rx="13.5"
        stroke="#787E89"
        strokeDasharray="6 6"
      />
      <circle cx="199.5" cy="105" r="25" fill="#6D11D2" />
      <path
        d="M200.5 96.0023C200.03 96 199.531 96 199 96C194.522 96 192.282 96 190.891 97.3912C189.5 98.7825 189.5 101.022 189.5 105.5C189.5 109.978 189.5 112.218 190.891 113.609C192.282 115 194.522 115 199 115C203.478 115 205.718 115 207.109 113.609C208.447 112.27 208.498 110.147 208.5 106"
        stroke="white"
        strokeWidth="1.5"
        strokeLinecap="round"
      />
      <path
        d="M189.5 107.135C190.119 107.046 190.745 107.001 191.372 107.003C194.024 106.947 196.611 107.773 198.671 109.334C200.582 110.782 201.925 112.775 202.5 115"
        stroke="white"
        strokeWidth="1.5"
        strokeLinejoin="round"
      />
      <path
        d="M208.5 109.896C207.325 109.301 206.109 108.999 204.886 109C203.034 108.993 201.202 109.673 199.5 111"
        stroke="white"
        strokeWidth="1.5"
        strokeLinejoin="round"
      />
      <path
        d="M204.5 97.5C204.992 96.9943 206.3 95 207 95M209.5 97.5C209.008 96.9943 207.7 95 207 95M207 95V103"
        stroke="white"
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M149.251 146.909H151.623V155.462C151.623 156.399 151.401 157.224 150.958 157.935C150.519 158.647 149.901 159.203 149.104 159.604C148.307 160 147.376 160.198 146.311 160.198C145.241 160.198 144.308 160 143.511 159.604C142.714 159.203 142.097 158.647 141.658 157.935C141.219 157.224 140.999 156.399 140.999 155.462V146.909H143.371V155.263C143.371 155.809 143.49 156.295 143.729 156.721C143.972 157.147 144.312 157.482 144.751 157.724C145.19 157.963 145.71 158.082 146.311 158.082C146.912 158.082 147.432 157.963 147.871 157.724C148.314 157.482 148.655 157.147 148.893 156.721C149.132 156.295 149.251 155.809 149.251 155.263V146.909ZM154.098 163.682V150.182H156.374V151.805H156.508C156.627 151.567 156.796 151.313 157.013 151.045C157.23 150.772 157.524 150.54 157.895 150.348C158.266 150.152 158.739 150.054 159.314 150.054C160.073 150.054 160.756 150.248 161.366 150.636C161.979 151.019 162.465 151.588 162.823 152.342C163.185 153.092 163.367 154.013 163.367 155.104C163.367 156.182 163.19 157.098 162.836 157.852C162.482 158.607 162.001 159.182 161.391 159.578C160.782 159.974 160.092 160.173 159.32 160.173C158.758 160.173 158.291 160.079 157.921 159.891C157.55 159.704 157.252 159.478 157.026 159.214C156.804 158.945 156.631 158.692 156.508 158.453H156.412V163.682H154.098ZM156.367 155.091C156.367 155.726 156.457 156.282 156.636 156.759C156.819 157.237 157.081 157.609 157.422 157.878C157.767 158.142 158.185 158.274 158.675 158.274C159.186 158.274 159.614 158.138 159.96 157.865C160.305 157.588 160.565 157.211 160.739 156.734C160.918 156.252 161.008 155.705 161.008 155.091C161.008 154.482 160.921 153.94 160.746 153.467C160.571 152.994 160.311 152.624 159.966 152.355C159.621 152.087 159.19 151.952 158.675 151.952C158.18 151.952 157.761 152.082 157.416 152.342C157.07 152.602 156.808 152.967 156.629 153.435C156.455 153.904 156.367 154.456 156.367 155.091ZM167.662 146.909V160H165.348V146.909H167.662ZM174.324 160.192C173.365 160.192 172.534 159.981 171.831 159.559C171.128 159.137 170.583 158.547 170.195 157.788C169.811 157.03 169.62 156.143 169.62 155.129C169.62 154.115 169.811 153.227 170.195 152.464C170.583 151.701 171.128 151.109 171.831 150.687C172.534 150.265 173.365 150.054 174.324 150.054C175.283 150.054 176.114 150.265 176.817 150.687C177.52 151.109 178.063 151.701 178.447 152.464C178.835 153.227 179.029 154.115 179.029 155.129C179.029 156.143 178.835 157.03 178.447 157.788C178.063 158.547 177.52 159.137 176.817 159.559C176.114 159.981 175.283 160.192 174.324 160.192ZM174.337 158.338C174.857 158.338 175.291 158.195 175.641 157.91C175.99 157.62 176.25 157.232 176.421 156.746C176.595 156.261 176.683 155.719 176.683 155.123C176.683 154.522 176.595 153.979 176.421 153.493C176.25 153.003 175.99 152.613 175.641 152.323C175.291 152.033 174.857 151.888 174.337 151.888C173.804 151.888 173.361 152.033 173.007 152.323C172.658 152.613 172.396 153.003 172.221 153.493C172.051 153.979 171.965 154.522 171.965 155.123C171.965 155.719 172.051 156.261 172.221 156.746C172.396 157.232 172.658 157.62 173.007 157.91C173.361 158.195 173.804 158.338 174.337 158.338ZM183.818 160.198C183.196 160.198 182.635 160.087 182.137 159.866C181.642 159.64 181.25 159.308 180.961 158.869C180.675 158.43 180.532 157.888 180.532 157.245C180.532 156.691 180.635 156.233 180.839 155.871C181.044 155.509 181.323 155.219 181.677 155.001C182.03 154.784 182.429 154.62 182.872 154.509C183.319 154.394 183.782 154.311 184.259 154.26C184.834 154.2 185.301 154.147 185.659 154.1C186.017 154.049 186.277 153.972 186.439 153.87C186.605 153.763 186.688 153.599 186.688 153.378V153.339C186.688 152.858 186.545 152.485 186.26 152.221C185.974 151.957 185.563 151.825 185.026 151.825C184.459 151.825 184.01 151.948 183.677 152.195C183.349 152.442 183.128 152.734 183.013 153.071L180.852 152.764C181.022 152.168 181.304 151.669 181.696 151.268C182.088 150.864 182.567 150.561 183.134 150.361C183.701 150.156 184.327 150.054 185.013 150.054C185.486 150.054 185.957 150.109 186.426 150.22C186.895 150.331 187.323 150.514 187.711 150.77C188.098 151.021 188.41 151.364 188.644 151.799C188.883 152.234 189.002 152.777 189.002 153.429V160H186.777V158.651H186.701C186.56 158.924 186.362 159.18 186.106 159.418C185.855 159.653 185.537 159.842 185.154 159.987C184.775 160.128 184.329 160.198 183.818 160.198ZM184.419 158.498C184.883 158.498 185.286 158.406 185.627 158.223C185.968 158.036 186.23 157.788 186.413 157.482C186.601 157.175 186.694 156.84 186.694 156.478V155.321C186.622 155.381 186.498 155.436 186.324 155.487C186.153 155.538 185.961 155.583 185.748 155.621C185.535 155.66 185.324 155.694 185.116 155.724C184.907 155.754 184.726 155.779 184.572 155.8C184.227 155.847 183.918 155.924 183.645 156.031C183.373 156.137 183.157 156.286 183 156.478C182.842 156.665 182.763 156.908 182.763 157.207C182.763 157.633 182.919 157.955 183.23 158.172C183.541 158.389 183.937 158.498 184.419 158.498ZM194.972 160.173C194.201 160.173 193.51 159.974 192.901 159.578C192.292 159.182 191.81 158.607 191.456 157.852C191.103 157.098 190.926 156.182 190.926 155.104C190.926 154.013 191.105 153.092 191.463 152.342C191.825 151.588 192.313 151.019 192.927 150.636C193.54 150.248 194.224 150.054 194.978 150.054C195.554 150.054 196.027 150.152 196.397 150.348C196.768 150.54 197.062 150.772 197.28 151.045C197.497 151.313 197.665 151.567 197.785 151.805H197.88V146.909H200.201V160H197.925V158.453H197.785C197.665 158.692 197.493 158.945 197.267 159.214C197.041 159.478 196.743 159.704 196.372 159.891C196.001 160.079 195.535 160.173 194.972 160.173ZM195.618 158.274C196.108 158.274 196.525 158.142 196.87 157.878C197.216 157.609 197.478 157.237 197.657 156.759C197.836 156.282 197.925 155.726 197.925 155.091C197.925 154.456 197.836 153.904 197.657 153.435C197.482 152.967 197.222 152.602 196.877 152.342C196.536 152.082 196.116 151.952 195.618 151.952C195.102 151.952 194.672 152.087 194.326 152.355C193.981 152.624 193.721 152.994 193.547 153.467C193.372 153.94 193.285 154.482 193.285 155.091C193.285 155.705 193.372 156.252 193.547 156.734C193.726 157.211 193.988 157.588 194.333 157.865C194.682 158.138 195.111 158.274 195.618 158.274ZM209.605 146.909V160H207.234V146.909H209.605ZM212.071 160V150.182H214.282V151.85H214.397C214.602 151.288 214.941 150.849 215.414 150.533C215.887 150.214 216.451 150.054 217.108 150.054C217.772 150.054 218.333 150.216 218.789 150.54C219.249 150.859 219.573 151.296 219.76 151.85H219.863C220.08 151.305 220.446 150.87 220.962 150.546C221.482 150.218 222.098 150.054 222.809 150.054C223.713 150.054 224.45 150.339 225.021 150.911C225.592 151.482 225.878 152.315 225.878 153.41V160H223.557V153.768C223.557 153.158 223.395 152.713 223.071 152.432C222.748 152.146 222.351 152.004 221.883 152.004C221.324 152.004 220.888 152.178 220.572 152.528C220.261 152.873 220.106 153.322 220.106 153.876V160H217.836V153.672C217.836 153.165 217.683 152.76 217.376 152.457C217.074 152.155 216.677 152.004 216.187 152.004C215.855 152.004 215.552 152.089 215.28 152.259C215.007 152.425 214.79 152.662 214.628 152.969C214.466 153.271 214.385 153.625 214.385 154.03V160H212.071ZM231.05 160.198C230.428 160.198 229.868 160.087 229.369 159.866C228.875 159.64 228.483 159.308 228.193 158.869C227.908 158.43 227.765 157.888 227.765 157.245C227.765 156.691 227.867 156.233 228.072 155.871C228.276 155.509 228.555 155.219 228.909 155.001C229.263 154.784 229.661 154.62 230.104 154.509C230.552 154.394 231.014 154.311 231.491 154.26C232.067 154.2 232.533 154.147 232.891 154.1C233.249 154.049 233.509 153.972 233.671 153.87C233.837 153.763 233.92 153.599 233.92 153.378V153.339C233.92 152.858 233.778 152.485 233.492 152.221C233.207 151.957 232.795 151.825 232.258 151.825C231.692 151.825 231.242 151.948 230.91 152.195C230.582 152.442 230.36 152.734 230.245 153.071L228.084 152.764C228.255 152.168 228.536 151.669 228.928 151.268C229.32 150.864 229.8 150.561 230.366 150.361C230.933 150.156 231.56 150.054 232.246 150.054C232.719 150.054 233.19 150.109 233.658 150.22C234.127 150.331 234.555 150.514 234.943 150.77C235.331 151.021 235.642 151.364 235.876 151.799C236.115 152.234 236.234 152.777 236.234 153.429V160H234.01V158.651H233.933C233.793 158.924 233.594 159.18 233.339 159.418C233.087 159.653 232.77 159.842 232.386 159.987C232.007 160.128 231.562 160.198 231.05 160.198ZM231.651 158.498C232.116 158.498 232.518 158.406 232.859 158.223C233.2 158.036 233.462 157.788 233.646 157.482C233.833 157.175 233.927 156.84 233.927 156.478V155.321C233.854 155.381 233.731 155.436 233.556 155.487C233.386 155.538 233.194 155.583 232.981 155.621C232.768 155.66 232.557 155.694 232.348 155.724C232.139 155.754 231.958 155.779 231.805 155.8C231.459 155.847 231.15 155.924 230.878 156.031C230.605 156.137 230.39 156.286 230.232 156.478C230.074 156.665 229.996 156.908 229.996 157.207C229.996 157.633 230.151 157.955 230.462 158.172C230.773 158.389 231.17 158.498 231.651 158.498ZM242.831 163.886C242 163.886 241.286 163.773 240.69 163.548C240.093 163.326 239.614 163.028 239.251 162.653C238.889 162.278 238.638 161.862 238.497 161.406L240.581 160.901C240.675 161.093 240.811 161.283 240.99 161.47C241.169 161.662 241.41 161.82 241.712 161.943C242.019 162.071 242.405 162.135 242.869 162.135C243.525 162.135 244.069 161.975 244.499 161.656C244.93 161.34 245.145 160.82 245.145 160.096V158.236H245.03C244.91 158.474 244.736 158.719 244.506 158.971C244.28 159.222 243.979 159.433 243.604 159.604C243.234 159.774 242.767 159.859 242.204 159.859C241.45 159.859 240.766 159.683 240.153 159.329C239.543 158.971 239.057 158.438 238.695 157.731C238.337 157.019 238.158 156.129 238.158 155.059C238.158 153.981 238.337 153.071 238.695 152.33C239.057 151.584 239.545 151.019 240.159 150.636C240.773 150.248 241.457 150.054 242.211 150.054C242.786 150.054 243.259 150.152 243.63 150.348C244.005 150.54 244.303 150.772 244.525 151.045C244.746 151.313 244.915 151.567 245.03 151.805H245.158V150.182H247.44V160.16C247.44 160.999 247.239 161.694 246.839 162.244C246.438 162.793 245.891 163.205 245.196 163.477C244.501 163.75 243.713 163.886 242.831 163.886ZM242.85 158.044C243.34 158.044 243.758 157.925 244.103 157.686C244.448 157.447 244.71 157.104 244.889 156.657C245.068 156.21 245.158 155.673 245.158 155.046C245.158 154.428 245.068 153.887 244.889 153.423C244.714 152.958 244.454 152.598 244.109 152.342C243.768 152.082 243.349 151.952 242.85 151.952C242.334 151.952 241.904 152.087 241.559 152.355C241.214 152.624 240.954 152.992 240.779 153.461C240.604 153.925 240.517 154.454 240.517 155.046C240.517 155.647 240.604 156.173 240.779 156.625C240.958 157.072 241.22 157.422 241.565 157.673C241.915 157.92 242.343 158.044 242.85 158.044ZM254.158 160.192C253.173 160.192 252.323 159.987 251.607 159.578C250.896 159.165 250.348 158.581 249.964 157.827C249.581 157.068 249.389 156.175 249.389 155.148C249.389 154.138 249.581 153.252 249.964 152.489C250.352 151.722 250.893 151.126 251.588 150.7C252.283 150.269 253.099 150.054 254.036 150.054C254.641 150.054 255.212 150.152 255.749 150.348C256.29 150.54 256.768 150.838 257.181 151.243C257.599 151.648 257.927 152.163 258.165 152.79C258.404 153.412 258.523 154.153 258.523 155.014V155.724H250.476V154.164H256.305C256.301 153.721 256.205 153.327 256.018 152.982C255.83 152.632 255.568 152.357 255.231 152.157C254.899 151.957 254.511 151.857 254.068 151.857C253.595 151.857 253.18 151.972 252.822 152.202C252.464 152.428 252.185 152.726 251.984 153.097C251.788 153.463 251.688 153.866 251.684 154.305V155.666C251.684 156.237 251.788 156.727 251.997 157.136C252.206 157.541 252.498 157.852 252.873 158.07C253.248 158.283 253.687 158.389 254.19 158.389C254.526 158.389 254.831 158.342 255.104 158.249C255.376 158.151 255.613 158.008 255.813 157.82C256.013 157.633 256.165 157.401 256.267 157.124L258.427 157.366C258.291 157.937 258.031 158.436 257.648 158.862C257.268 159.284 256.783 159.612 256.19 159.847C255.598 160.077 254.92 160.192 254.158 160.192Z"
        fill="#2D394A"
      />
      <path
        d="M140.693 184V173.818H146.585V174.653H141.623V178.487H146.122V179.322H141.623V184H140.693ZM150.42 184.159C149.76 184.159 149.177 183.992 148.67 183.657C148.166 183.322 147.772 182.86 147.487 182.27C147.202 181.677 147.059 180.992 147.059 180.217C147.059 179.434 147.202 178.747 147.487 178.153C147.772 177.557 148.166 177.093 148.67 176.761C149.177 176.427 149.76 176.259 150.42 176.259C151.08 176.259 151.661 176.427 152.165 176.761C152.669 177.096 153.063 177.56 153.348 178.153C153.637 178.747 153.781 179.434 153.781 180.217C153.781 180.992 153.638 181.677 153.353 182.27C153.068 182.86 152.672 183.322 152.165 183.657C151.661 183.992 151.08 184.159 150.42 184.159ZM150.42 183.349C150.95 183.349 151.399 183.206 151.767 182.921C152.135 182.636 152.414 182.257 152.602 181.783C152.795 181.309 152.891 180.787 152.891 180.217C152.891 179.647 152.795 179.123 152.602 178.646C152.414 178.168 152.135 177.786 151.767 177.497C151.399 177.209 150.95 177.065 150.42 177.065C149.893 177.065 149.444 177.209 149.073 177.497C148.705 177.786 148.425 178.168 148.232 178.646C148.044 179.123 147.949 179.647 147.949 180.217C147.949 180.787 148.044 181.309 148.232 181.783C148.425 182.257 148.705 182.636 149.073 182.921C149.441 183.206 149.89 183.349 150.42 183.349ZM155.023 184V176.364H155.883V177.537H155.948C156.101 177.152 156.366 176.843 156.744 176.607C157.125 176.369 157.556 176.249 158.036 176.249C158.109 176.249 158.19 176.251 158.28 176.254C158.369 176.258 158.444 176.261 158.504 176.264V177.164C158.464 177.157 158.394 177.147 158.295 177.134C158.195 177.121 158.088 177.114 157.972 177.114C157.574 177.114 157.219 177.199 156.908 177.368C156.599 177.534 156.356 177.764 156.177 178.059C155.998 178.354 155.908 178.69 155.908 179.068V184H155.023ZM159.314 184V176.364H160.174V177.537H160.253C160.406 177.143 160.659 176.833 161.014 176.607C161.372 176.379 161.801 176.264 162.302 176.264C162.829 176.264 163.261 176.388 163.599 176.637C163.941 176.882 164.196 177.22 164.365 177.651H164.429C164.602 177.227 164.882 176.891 165.27 176.642C165.661 176.39 166.135 176.264 166.691 176.264C167.401 176.264 167.961 176.488 168.372 176.935C168.783 177.379 168.988 178.029 168.988 178.884V184H168.103V178.884C168.103 178.281 167.949 177.829 167.641 177.527C167.333 177.225 166.927 177.075 166.423 177.075C165.84 177.075 165.391 177.254 165.076 177.612C164.761 177.969 164.603 178.424 164.603 178.974V184H163.699V178.805C163.699 178.288 163.554 177.87 163.266 177.552C162.978 177.234 162.572 177.075 162.048 177.075C161.697 177.075 161.38 177.161 161.098 177.333C160.82 177.505 160.6 177.746 160.437 178.054C160.278 178.359 160.199 178.71 160.199 179.108V184H159.314ZM172.764 184.174C172.303 184.174 171.882 184.085 171.501 183.906C171.12 183.723 170.817 183.461 170.591 183.12C170.366 182.775 170.253 182.358 170.253 181.867C170.253 181.489 170.325 181.171 170.467 180.913C170.61 180.654 170.812 180.442 171.074 180.276C171.336 180.111 171.645 179.98 172.003 179.884C172.361 179.787 172.756 179.713 173.187 179.66C173.614 179.607 173.975 179.56 174.27 179.521C174.569 179.481 174.796 179.418 174.952 179.332C175.107 179.246 175.185 179.106 175.185 178.914V178.735C175.185 178.215 175.029 177.805 174.718 177.507C174.41 177.205 173.965 177.055 173.385 177.055C172.835 177.055 172.386 177.176 172.038 177.418C171.693 177.66 171.452 177.945 171.312 178.273L170.472 177.969C170.644 177.552 170.883 177.219 171.188 176.97C171.493 176.718 171.834 176.538 172.212 176.428C172.59 176.316 172.973 176.259 173.361 176.259C173.652 176.259 173.956 176.297 174.27 176.374C174.589 176.45 174.884 176.582 175.155 176.771C175.427 176.957 175.648 177.217 175.817 177.552C175.986 177.883 176.07 178.304 176.07 178.815V184H175.185V182.792H175.131C175.024 183.017 174.867 183.236 174.658 183.448C174.449 183.66 174.188 183.834 173.873 183.97C173.558 184.106 173.188 184.174 172.764 184.174ZM172.883 183.364C173.354 183.364 173.762 183.259 174.106 183.05C174.451 182.842 174.716 182.565 174.902 182.22C175.091 181.872 175.185 181.489 175.185 181.072V179.968C175.119 180.031 175.008 180.087 174.852 180.137C174.7 180.187 174.522 180.232 174.32 180.271C174.121 180.308 173.922 180.339 173.724 180.366C173.525 180.392 173.346 180.415 173.187 180.435C172.756 180.488 172.388 180.571 172.083 180.684C171.778 180.797 171.544 180.952 171.382 181.151C171.22 181.347 171.138 181.599 171.138 181.907C171.138 182.371 171.304 182.731 171.635 182.986C171.967 183.238 172.383 183.364 172.883 183.364ZM180.732 176.364V177.134H177.088V176.364H180.732ZM178.226 174.534H179.116V182.006C179.116 182.325 179.171 182.575 179.28 182.757C179.39 182.936 179.532 183.064 179.708 183.14C179.884 183.213 180.071 183.249 180.27 183.249C180.386 183.249 180.485 183.243 180.568 183.229C180.651 183.213 180.724 183.196 180.787 183.18L180.976 183.98C180.889 184.013 180.783 184.043 180.657 184.07C180.531 184.099 180.376 184.114 180.19 184.114C179.865 184.114 179.552 184.043 179.25 183.901C178.952 183.758 178.707 183.546 178.515 183.264C178.322 182.982 178.226 182.633 178.226 182.215V174.534ZM187.036 178.039L186.225 178.268C186.146 178.039 186.033 177.832 185.887 177.646C185.741 177.461 185.551 177.313 185.316 177.204C185.084 177.094 184.795 177.04 184.45 177.04C183.933 177.04 183.509 177.162 183.178 177.408C182.846 177.653 182.681 177.969 182.681 178.357C182.681 178.685 182.793 178.952 183.019 179.158C183.247 179.36 183.599 179.521 184.073 179.64L185.226 179.923C185.866 180.079 186.345 180.326 186.663 180.664C186.984 181.002 187.145 181.426 187.145 181.937C187.145 182.368 187.026 182.75 186.787 183.085C186.549 183.42 186.215 183.683 185.788 183.876C185.364 184.065 184.871 184.159 184.311 184.159C183.566 184.159 182.951 183.992 182.467 183.657C181.983 183.319 181.673 182.832 181.537 182.195L182.387 181.987C182.497 182.441 182.71 182.784 183.029 183.016C183.35 183.248 183.773 183.364 184.296 183.364C184.883 183.364 185.352 183.233 185.703 182.971C186.055 182.706 186.23 182.374 186.23 181.977C186.23 181.668 186.128 181.41 185.922 181.201C185.717 180.989 185.405 180.833 184.987 180.734L183.74 180.435C183.077 180.276 182.586 180.024 182.268 179.68C181.95 179.335 181.791 178.907 181.791 178.397C181.791 177.976 181.905 177.607 182.134 177.288C182.362 176.967 182.677 176.715 183.078 176.533C183.479 176.35 183.937 176.259 184.45 176.259C185.15 176.259 185.708 176.418 186.126 176.737C186.547 177.051 186.85 177.486 187.036 178.039ZM189.089 183.806C188.893 183.806 188.724 183.737 188.582 183.597C188.442 183.458 188.373 183.289 188.373 183.09C188.373 182.895 188.442 182.727 188.582 182.588C188.724 182.446 188.893 182.374 189.089 182.374C189.288 182.374 189.457 182.446 189.596 182.588C189.735 182.727 189.805 182.895 189.805 183.09C189.805 183.223 189.772 183.344 189.705 183.453C189.642 183.559 189.556 183.645 189.447 183.712C189.341 183.775 189.221 183.806 189.089 183.806ZM189.089 178.298C188.893 178.298 188.724 178.228 188.582 178.089C188.442 177.95 188.373 177.781 188.373 177.582C188.373 177.386 188.442 177.219 188.582 177.08C188.724 176.937 188.893 176.866 189.089 176.866C189.288 176.866 189.457 176.937 189.596 177.08C189.735 177.219 189.805 177.386 189.805 177.582C189.805 177.714 189.772 177.835 189.705 177.945C189.642 178.051 189.556 178.137 189.447 178.203C189.341 178.266 189.221 178.298 189.089 178.298ZM198.722 173.818H199.652V181.161C199.648 181.824 199.529 182.378 199.294 182.822C199.058 183.263 198.734 183.592 198.319 183.811C197.905 184.03 197.429 184.139 196.892 184.139C196.365 184.139 195.895 184.041 195.48 183.846C195.066 183.65 194.74 183.375 194.501 183.021C194.266 182.666 194.148 182.25 194.148 181.773H195.063C195.063 182.074 195.142 182.341 195.301 182.573C195.461 182.802 195.678 182.981 195.953 183.11C196.231 183.239 196.544 183.304 196.892 183.304C197.254 183.304 197.57 183.228 197.842 183.075C198.117 182.923 198.333 182.689 198.488 182.374C198.644 182.056 198.722 181.652 198.722 181.161V173.818ZM201.656 184V173.818H204.942C205.665 173.818 206.268 173.957 206.752 174.236C207.239 174.511 207.606 174.885 207.851 175.359C208.099 175.833 208.224 176.367 208.224 176.96C208.224 177.554 208.101 178.089 207.856 178.566C207.61 179.04 207.246 179.416 206.762 179.695C206.278 179.97 205.677 180.107 204.957 180.107H202.372V179.272H204.932C205.466 179.272 205.909 179.173 206.26 178.974C206.611 178.772 206.873 178.496 207.045 178.148C207.221 177.8 207.309 177.404 207.309 176.96C207.309 176.516 207.221 176.12 207.045 175.772C206.873 175.424 206.61 175.151 206.255 174.952C205.904 174.753 205.458 174.653 204.918 174.653H202.586V184H201.656ZM209.706 184V173.818H215.612V174.653H210.636V178.487H215.299V179.322H210.636V183.165H215.712V184H209.706ZM224.363 177C224.271 176.662 224.135 176.345 223.956 176.05C223.777 175.752 223.555 175.492 223.29 175.27C223.028 175.045 222.724 174.869 222.38 174.743C222.038 174.617 221.657 174.554 221.236 174.554C220.573 174.554 219.98 174.726 219.456 175.071C218.933 175.416 218.518 175.913 218.214 176.562C217.912 177.212 217.761 177.994 217.761 178.909C217.761 179.821 217.914 180.601 218.219 181.251C218.523 181.9 218.941 182.399 219.471 182.747C220.005 183.092 220.615 183.264 221.301 183.264C221.927 183.264 222.481 183.125 222.961 182.847C223.442 182.568 223.817 182.17 224.085 181.653C224.357 181.136 224.489 180.522 224.483 179.809L224.781 179.903H221.52V179.068H225.388V179.903C225.388 180.772 225.212 181.522 224.861 182.156C224.509 182.789 224.027 183.277 223.414 183.622C222.801 183.967 222.096 184.139 221.301 184.139C220.413 184.139 219.635 183.925 218.969 183.498C218.303 183.067 217.784 182.46 217.413 181.678C217.045 180.893 216.861 179.97 216.861 178.909C216.861 178.11 216.967 177.389 217.179 176.746C217.392 176.103 217.692 175.553 218.079 175.096C218.47 174.638 218.933 174.289 219.466 174.047C220 173.802 220.59 173.679 221.236 173.679C221.793 173.679 222.302 173.767 222.763 173.942C223.227 174.118 223.634 174.36 223.986 174.668C224.34 174.973 224.63 175.326 224.856 175.727C225.084 176.125 225.238 176.549 225.318 177H224.363ZM228.283 182.608L228.194 183.14C228.137 183.498 228.058 183.892 227.955 184.323C227.852 184.754 227.746 185.163 227.637 185.551C227.528 185.939 227.433 186.25 227.354 186.486H226.672C226.716 186.26 226.77 185.974 226.837 185.626C226.906 185.278 226.977 184.892 227.05 184.467C227.123 184.046 227.19 183.612 227.249 183.165L227.319 182.608H228.283ZM233.348 184V173.818H236.634C237.356 173.818 237.96 173.957 238.443 174.236C238.931 174.511 239.297 174.885 239.542 175.359C239.791 175.833 239.915 176.367 239.915 176.96C239.915 177.554 239.792 178.089 239.547 178.566C239.302 179.04 238.937 179.416 238.453 179.695C237.97 179.97 237.368 180.107 236.649 180.107H234.063V179.272H236.624C237.157 179.272 237.6 179.173 237.951 178.974C238.303 178.772 238.564 178.496 238.737 178.148C238.912 177.8 239 177.404 239 176.96C239 176.516 238.912 176.12 238.737 175.772C238.564 175.424 238.301 175.151 237.946 174.952C237.595 174.753 237.149 174.653 236.609 174.653H234.277V184H233.348ZM249.327 173.818V184H248.422L242.407 175.474H242.327V184H241.398V173.818H242.297L248.333 182.354H248.413V173.818H249.327ZM258.379 177C258.286 176.662 258.15 176.345 257.971 176.05C257.792 175.752 257.57 175.492 257.305 175.27C257.043 175.045 256.74 174.869 256.395 174.743C256.054 174.617 255.673 174.554 255.252 174.554C254.589 174.554 253.996 174.726 253.472 175.071C252.948 175.416 252.534 175.913 252.229 176.562C251.928 177.212 251.777 177.994 251.777 178.909C251.777 179.821 251.929 180.601 252.234 181.251C252.539 181.9 252.957 182.399 253.487 182.747C254.021 183.092 254.63 183.264 255.317 183.264C255.943 183.264 256.496 183.125 256.977 182.847C257.458 182.568 257.832 182.17 258.101 181.653C258.372 181.136 258.505 180.522 258.498 179.809L258.797 179.903H255.535V179.068H259.403V179.903C259.403 180.772 259.228 181.522 258.876 182.156C258.525 182.789 258.043 183.277 257.429 183.622C256.816 183.967 256.112 184.139 255.317 184.139C254.428 184.139 253.651 183.925 252.985 183.498C252.319 183.067 251.8 182.46 251.429 181.678C251.061 180.893 250.877 179.97 250.877 178.909C250.877 178.11 250.983 177.389 251.195 176.746C251.407 176.103 251.707 175.553 252.095 175.096C252.486 174.638 252.948 174.289 253.482 174.047C254.016 173.802 254.606 173.679 255.252 173.679C255.809 173.679 256.317 173.767 256.778 173.942C257.242 174.118 257.65 174.36 258.001 174.668C258.356 174.973 258.646 175.326 258.871 175.727C259.1 176.125 259.254 176.549 259.334 177H258.379Z"
        fill="#787E89"
      />
    </svg>
  );
};

export const CoverImageIcon = ({ size = 24, stroke = "#2D394A" }) => {
  return (
    <svg
      width={size}
      height={size}
      viewBox="0 0 24 24"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M15 8H15.01"
        stroke={stroke}
        strokeWidth="2"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M17 4H7C5.34315 4 4 5.34315 4 7V17C4 18.6569 5.34315 20 7 20H17C18.6569 20 20 18.6569 20 17V7C20 5.34315 18.6569 4 17 4Z"
        stroke={stroke}
        strokeWidth="2"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M4 15.0019L8 11.0019C8.45606 10.5631 8.97339 10.332 9.5 10.332C10.0266 10.332 10.5439 10.5631 11 11.0019L16 16.0019"
        stroke={stroke}
        strokeWidth="2"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M14 14.0019L15 13.0019C15.4561 12.5631 15.9734 12.332 16.5 12.332C17.0266 12.332 17.5439 12.5631 18 13.0019L20 15.0019"
        stroke={stroke}
        strokeWidth="2"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  );
};

export const PrivateProjectIcon = ({ size = 20, stroke = "#fff" }) => {
  return (
    <svg
      width={size}
      height={size}
      viewBox="0 0 20 20"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M9.9994 2.38477C7.70839 2.38477 6.22289 3.92263 4.46606 4.48317C3.75172 4.71109 3.39455 4.82505 3.25 4.9857C3.10546 5.14634 3.06313 5.38109 2.97847 5.85059C2.0726 10.8746 4.0526 15.5195 8.77466 17.3273C9.28202 17.5216 9.5357 17.6187 10.0019 17.6187C10.4681 17.6187 10.7218 17.5216 11.2291 17.3273C15.9509 15.5194 17.929 10.8746 17.0228 5.85058C16.9381 5.38101 16.8958 5.14622 16.7512 4.98557C16.6066 4.82492 16.2495 4.71103 15.5352 4.48323C13.7777 3.92274 12.2905 2.38477 9.9994 2.38477Z"
        stroke={stroke}
        strokeWidth="1.14255"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M8.15783 12.9399L7.92609 13.4621L7.92609 13.4621L8.15783 12.9399ZM7.43643 12.167L6.90359 12.373H6.90359L7.43643 12.167ZM12.5654 12.167L13.0982 12.373L13.0982 12.373L12.5654 12.167ZM11.844 12.9399L12.0757 13.4621L12.0757 13.4621L11.844 12.9399ZM11.844 8.8728L12.0757 8.35064L11.844 8.8728ZM12.5654 9.64573L13.0982 9.43973L13.0982 9.43973L12.5654 9.64573ZM8.15783 8.8728L7.92609 8.35064H7.92609L8.15783 8.8728ZM7.43643 9.64573L6.90359 9.43973L7.43643 9.64573ZM8.09666 8.76409C8.09666 9.07959 8.35243 9.33536 8.66793 9.33536C8.98344 9.33536 9.2392 9.07959 9.2392 8.76409H8.09666ZM10.7626 8.76409C10.7626 9.07959 11.0184 9.33536 11.3339 9.33536C11.6494 9.33536 11.9051 9.07959 11.9051 8.76409H10.7626ZM9.33442 9.33536H10.6674V8.19281H9.33442V9.33536ZM10.6674 12.4774H9.33442V13.6199H10.6674V12.4774ZM9.33442 12.4774C9.0155 12.4774 8.80696 12.477 8.64672 12.4653C8.49245 12.454 8.42709 12.4344 8.38956 12.4178L7.92609 13.4621C8.13352 13.5541 8.34592 13.5889 8.56342 13.6048C8.77497 13.6203 9.03224 13.6199 9.33442 13.6199V12.4774ZM6.76369 10.9064C6.76369 11.2318 6.76342 11.5026 6.77753 11.7241C6.79192 11.9501 6.82292 12.1643 6.90359 12.373L7.96927 11.961C7.94846 11.9072 7.92874 11.8239 7.91776 11.6515C7.9065 11.4747 7.90623 11.2464 7.90623 10.9064H6.76369ZM8.38956 12.4178C8.20871 12.3375 8.05304 12.1777 7.96927 11.961L6.90359 12.373C7.09039 12.8562 7.45371 13.2524 7.92609 13.4621L8.38956 12.4178ZM12.0956 10.9064C12.0956 11.2464 12.0953 11.4747 12.084 11.6515C12.0731 11.8239 12.0533 11.9072 12.0325 11.961L13.0982 12.373C13.1789 12.1643 13.2099 11.9501 13.2243 11.7241C13.2384 11.5026 13.2381 11.2318 13.2381 10.9064H12.0956ZM10.6674 13.6199C10.9696 13.6199 11.2268 13.6203 11.4384 13.6048C11.6559 13.5889 11.8683 13.5541 12.0757 13.4621L11.6122 12.4178C11.5747 12.4344 11.5094 12.454 11.3551 12.4653C11.1948 12.477 10.9863 12.4774 10.6674 12.4774V13.6199ZM12.0325 11.961C11.9488 12.1777 11.7931 12.3375 11.6122 12.4178L12.0757 13.4621C12.5481 13.2524 12.9114 12.8562 13.0982 12.373L12.0325 11.961ZM10.6674 9.33536C10.9863 9.33536 11.1948 9.33572 11.3551 9.34743C11.5094 9.35871 11.5747 9.37831 11.6122 9.39496L12.0757 8.35064C11.8683 8.25858 11.6559 8.22382 11.4384 8.20792C11.2268 8.19246 10.9696 8.19281 10.6674 8.19281V9.33536ZM13.2381 10.9064C13.2381 10.5809 13.2384 10.3102 13.2243 10.0886C13.2099 9.86266 13.1789 9.64838 13.0982 9.43973L12.0325 9.85172C12.0533 9.90553 12.0731 9.98885 12.084 10.1612C12.0953 10.338 12.0956 10.5663 12.0956 10.9064H13.2381ZM11.6122 9.39496C11.7931 9.47522 11.9488 9.63504 12.0325 9.85172L13.0982 9.43973C12.9114 8.95653 12.5481 8.56029 12.0757 8.35064L11.6122 9.39496ZM9.33442 8.19281C9.03224 8.19281 8.77497 8.19246 8.56342 8.20792C8.34592 8.22382 8.13352 8.25858 7.92609 8.35064L8.38956 9.39496C8.42709 9.37831 8.49245 9.35871 8.64672 9.34743C8.80696 9.33572 9.0155 9.33536 9.33442 9.33536V8.19281ZM7.90623 10.9064C7.90623 10.5663 7.9065 10.338 7.91776 10.1612C7.92874 9.98885 7.94846 9.90553 7.96927 9.85172L6.90359 9.43973C6.82292 9.64838 6.79192 9.86266 6.77753 10.0886C6.76342 10.3102 6.76369 10.5809 6.76369 10.9064H7.90623ZM7.92609 8.35064C7.45371 8.56029 7.09039 8.95653 6.90359 9.43973L7.96927 9.85172C8.05304 9.63504 8.20871 9.47522 8.38956 9.39496L7.92609 8.35064ZM9.2392 8.76409V7.47872H8.09666V8.76409H9.2392ZM10.7626 7.47872V8.76409H11.9051V7.47872L10.7626 7.47872ZM10.0009 6.76463C10.4413 6.76463 10.7626 7.10375 10.7626 7.47872L11.9051 7.47872C11.9051 6.43393 11.0328 5.62209 10.0009 5.62209V6.76463ZM9.2392 7.47872C9.2392 7.10375 9.56047 6.76463 10.0009 6.76463V5.62209C8.96898 5.62209 8.09666 6.43393 8.09666 7.47872H9.2392Z"
        fill={stroke}
      />
    </svg>
  );
};

export const NoCommunityData = ({ width = 334, height = 220 }) => {
  return (
    <svg
      width={width}
      height={height}
      viewBox="0 0 334 220"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M72.5914 128.473C57.121 113.233 50.587 97.334 54.1926 83.7049C58.8197 66.2148 79.3757 54.1219 110.59 50.5269L110.705 51.5301C79.9255 55.0748 59.6831 66.8964 55.1681 83.9632C51.6591 97.227 58.0982 112.779 73.2994 127.754L72.5914 128.473Z"
        fill="#F0F0F0"
      />
      <path
        d="M221.855 182.571C202.547 182.571 181.565 179.699 160.725 174.178C134.952 167.351 111.322 157.045 92.3879 144.375L92.9486 143.536C111.791 156.145 135.317 166.403 160.983 173.202C184.176 179.347 207.544 182.194 228.551 181.443L228.587 182.451C226.369 182.531 224.125 182.571 221.855 182.571Z"
        fill="#F0F0F0"
      />
      <path
        d="M266.15 176.221L265.827 175.265C283.095 169.422 293.896 160.01 297.061 148.046C301.085 132.837 292.343 115.187 272.447 98.3485C252.449 81.4243 223.612 67.3816 191.247 58.8074L191.505 57.8313C224.012 66.4431 252.989 80.5586 273.098 97.5776C293.309 114.682 302.166 132.697 298.037 148.304C294.781 160.612 283.754 170.265 266.15 176.221Z"
        fill="#F0F0F0"
      />
      <path
        d="M107.586 182.993C69.6466 166.155 40.4116 144.309 25.2666 121.48L26.1071 120.922C41.1474 143.593 70.2289 165.309 107.995 182.07L107.586 182.993Z"
        fill="#F0F0F0"
      />
      <path
        d="M240.977 212.163C237.063 212.163 233.083 212.076 229.036 211.901L229.08 210.892C259.281 212.201 285.927 208.584 306.144 200.431L306.522 201.368C288.886 208.479 266.407 212.163 240.977 212.163Z"
        fill="#F0F0F0"
      />
      <path
        d="M218.964 51.4322C212.693 49.3892 206.243 47.4869 199.793 45.7782C162.676 35.9452 126.19 32.454 94.2768 35.6839L94.1753 34.6791C126.207 31.4386 162.819 34.9382 200.051 44.802C206.52 46.5157 212.988 48.4234 219.276 50.4723L218.964 51.4322Z"
        fill="#F0F0F0"
      />
      <path
        d="M48.8804 129.988C33.4099 114.748 26.876 98.8484 30.4815 85.2193C35.1086 67.7292 55.6646 55.6363 86.8786 52.0413L86.9939 53.0445C56.2144 56.5892 35.972 68.4108 31.457 85.4776C27.948 98.7414 34.3871 114.293 49.5883 129.268L48.8804 129.988Z"
        fill="#F0F0F0"
      />
      <path
        d="M198.144 184.085C178.836 184.085 157.854 181.214 137.014 175.693C111.241 168.865 87.6109 158.56 68.677 145.89L69.2376 145.051C88.0804 157.659 111.606 167.918 137.272 174.717C160.465 180.861 183.833 183.709 204.84 182.957L204.876 183.966C202.658 184.046 200.414 184.086 198.144 184.085Z"
        fill="#F0F0F0"
      />
      <path
        d="M242.439 177.736L242.116 176.779C259.384 170.937 270.185 161.525 273.35 149.561C277.374 134.351 268.632 116.702 248.736 99.8631C228.738 82.939 199.901 68.8962 167.536 60.3221L167.794 59.3459C200.301 67.9578 229.278 82.0733 249.388 99.0923C269.598 116.197 278.455 134.212 274.326 149.819C271.07 162.126 260.043 171.78 242.439 177.736Z"
        fill="#F0F0F0"
      />
      <path
        d="M83.8747 184.507C45.9355 167.669 16.7005 145.824 1.55554 122.995L2.39603 122.436C17.4364 145.107 46.5179 166.823 84.2836 183.584L83.8747 184.507Z"
        fill="#F0F0F0"
      />
      <path
        d="M217.266 213.678C213.353 213.678 209.372 213.59 205.326 213.416L205.369 212.407C235.57 213.715 262.217 210.099 282.434 201.946L282.811 202.883C265.175 209.994 242.697 213.678 217.266 213.678Z"
        fill="#F0F0F0"
      />
      <path
        d="M195.253 52.9469C188.982 50.9038 182.532 49.0016 176.082 47.2928C138.965 37.4598 102.479 33.9686 70.5658 37.1985L70.4644 36.1938C102.496 32.9533 139.108 36.4529 176.341 46.3167C182.809 48.0304 189.277 49.938 195.565 51.987L195.253 52.9469Z"
        fill="#F0F0F0"
      />
      <path
        d="M18.753 102.944C26.0036 94.8092 37.5697 89.8511 48.0511 92.8191C38.3815 99.6667 31.4441 109.716 28.4661 121.19C27.3337 125.619 26.535 130.663 22.8542 133.371C20.5639 135.056 17.5293 135.458 14.7127 135.074C11.896 134.691 9.23274 133.595 6.60542 132.508L5.89844 132.57C7.93107 121.861 11.5023 111.079 18.753 102.944Z"
        fill="#F2F2F2"
      />
      <path
        d="M48.0191 93.0456C39.2559 95.1689 31.384 100.001 25.5216 106.856C24.2334 108.31 23.1821 109.957 22.4066 111.738C21.7071 113.497 21.4911 115.412 21.7812 117.283C22.0166 119.033 22.4599 120.796 22.2661 122.572C22.1534 123.48 21.8501 124.354 21.3763 125.137C20.9024 125.919 20.2685 126.593 19.5163 127.113C17.6757 128.458 15.462 129.061 13.2767 129.575C10.8503 130.146 8.32095 130.701 6.34304 132.315C6.10339 132.51 5.81424 132.127 6.05353 131.931C9.49475 129.124 14.2773 129.484 18.0846 127.445C19.8612 126.494 21.362 125.001 21.7269 122.952C22.046 121.16 21.5904 119.342 21.3368 117.572C21.0271 115.751 21.1678 113.882 21.7464 112.128C22.4289 110.313 23.415 108.628 24.6624 107.144C27.4177 103.759 30.6876 100.829 34.3524 98.4606C38.5201 95.7343 43.1232 93.7415 47.9624 92.5685C48.2619 92.496 48.3167 92.9736 48.0191 93.0456Z"
        fill="white"
      />
      <path
        d="M27.2794 104.538C26.2636 103.397 25.686 101.933 25.6497 100.406C25.6133 98.8783 26.1207 97.3879 27.0811 96.2004C27.2763 95.9608 27.6575 96.253 27.4621 96.4929C26.5657 97.5971 26.0932 98.9855 26.13 100.408C26.1667 101.83 26.7102 103.192 27.6624 104.248C27.8691 104.478 27.4849 104.766 27.2794 104.538Z"
        fill="white"
      />
      <path
        d="M21.545 116.411C24.6589 116.618 27.7363 115.643 30.1637 113.68C30.404 113.486 30.6932 113.869 30.4532 114.064C27.9234 116.101 24.7188 117.11 21.4789 116.887C21.1701 116.866 21.2379 116.39 21.545 116.411Z"
        fill="white"
      />
      <path
        d="M38.6281 96.362C38.9292 96.8852 39.3555 97.3253 39.8687 97.6429C40.3819 97.9605 40.9658 98.1456 41.5681 98.1815C41.877 98.199 41.8089 98.6747 41.502 98.6574C40.8368 98.6146 40.1921 98.4094 39.6243 98.0598C39.0566 97.7101 38.5831 97.2267 38.2451 96.6517C38.2087 96.6002 38.1933 96.5367 38.2019 96.4742C38.2106 96.4117 38.2428 96.3548 38.2918 96.3152C38.3427 96.2771 38.4066 96.2606 38.4696 96.2694C38.5325 96.2781 38.5895 96.3114 38.6281 96.362Z"
        fill="white"
      />
      <path
        d="M62.9297 126.59C62.7365 126.584 62.5433 126.579 62.3476 126.577C59.7498 126.517 57.1514 126.669 54.5781 127.03C54.3787 127.055 54.177 127.083 53.9788 127.114C47.7884 128.051 41.8258 130.13 36.3939 133.245C34.2335 134.487 32.1707 135.891 30.2233 137.446C27.534 139.593 24.7887 142.136 21.6402 143.236C21.313 143.355 20.9788 143.453 20.6395 143.53L6.02804 133.428C6.00634 133.388 5.9822 133.352 5.96034 133.312L5.35852 132.936C5.45336 132.848 5.5536 132.76 5.64844 132.673C5.70324 132.622 5.76132 132.573 5.8161 132.523C5.85367 132.489 5.89142 132.456 5.92342 132.424C5.93589 132.413 5.94851 132.402 5.95869 132.394C5.9907 132.362 6.02383 132.336 6.05353 132.307C6.61246 131.811 7.17586 131.319 7.7437 130.829C7.74601 130.826 7.74601 130.826 7.75157 130.825C12.0878 127.105 16.7724 123.716 21.8223 121.145C21.9742 121.068 22.1283 120.987 22.287 120.914C24.5696 119.768 26.9421 118.811 29.3806 118.052C30.7172 117.64 32.0738 117.297 33.4451 117.023C36.9897 116.321 40.6225 116.182 44.2104 116.61C51.3627 117.464 58.1623 120.598 62.5952 126.162C62.7085 126.304 62.8187 126.444 62.9297 126.59Z"
        fill="#F2F2F2"
      />
      <path
        d="M62.7686 126.753C54.4943 123.169 45.3017 122.284 36.4965 124.226C34.5935 124.61 32.7629 125.292 31.0722 126.247C29.4552 127.23 28.1309 128.628 27.2368 130.297C26.3718 131.836 25.6652 133.511 24.4415 134.813C23.8052 135.47 23.0373 135.985 22.1881 136.324C21.3389 136.663 20.4277 136.819 19.5141 136.781C17.2353 136.746 15.105 135.894 13.0507 134.988C10.7697 133.982 8.41646 132.901 5.86637 132.998C5.55739 133.01 5.55728 132.529 5.86579 132.517C10.3025 132.349 13.9042 135.518 18.1711 136.184C20.1621 136.495 22.2587 136.207 23.7827 134.791C25.1153 133.553 25.8455 131.826 26.7078 130.26C27.5562 128.62 28.7931 127.212 30.3106 126.16C31.9473 125.122 33.7485 124.371 35.6372 123.938C39.8736 122.895 44.2475 122.526 48.5987 122.843C53.5666 123.177 58.4408 124.359 63.0105 126.338C63.2932 126.46 63.0496 126.875 62.7686 126.753Z"
        fill="white"
      />
      <path
        d="M39.295 123.433C39.1701 121.911 39.5901 120.393 40.4799 119.152C41.3698 117.911 42.6717 117.026 44.153 116.657C44.4529 116.583 44.5815 117.046 44.2811 117.12C42.9011 117.461 41.6885 118.285 40.8622 119.443C40.0359 120.601 39.6504 122.016 39.7751 123.433C39.8019 123.741 39.3216 123.739 39.295 123.433Z"
        fill="white"
      />
      <path
        d="M27.5726 129.459C29.9343 131.5 32.9781 132.576 36.0971 132.471C36.4061 132.46 36.4063 132.941 36.0977 132.951C32.8518 133.054 29.6865 131.928 27.2335 129.799C26.9999 129.596 27.3402 129.257 27.5726 129.459Z"
        fill="white"
      />
      <path
        d="M53.2751 123.743C53.2008 124.342 53.2763 124.95 53.495 125.513C53.7136 126.076 54.0685 126.575 54.5278 126.967C54.764 127.167 54.4233 127.506 54.1888 127.307C53.6833 126.872 53.292 126.32 53.0491 125.699C52.8061 125.077 52.7189 124.406 52.795 123.743C52.7969 123.68 52.8228 123.62 52.8673 123.576C52.9119 123.531 52.9717 123.505 53.0348 123.503C53.0983 123.503 53.1592 123.528 53.2042 123.573C53.2492 123.618 53.2747 123.679 53.2751 123.743Z"
        fill="white"
      />
      <path
        d="M302.86 169.631C298.763 159.531 289.535 150.971 278.667 150.241C285.474 159.943 288.632 171.741 287.583 183.548C287.162 188.101 286.22 193.12 288.778 196.908C290.369 199.266 293.092 200.665 295.873 201.251C298.655 201.837 301.531 201.7 304.371 201.561L305.016 201.857C306.698 191.087 306.956 179.732 302.86 169.631Z"
        fill="#F2F2F2"
      />
      <path
        d="M278.621 150.465C286.161 155.413 291.952 162.611 295.171 171.039C295.896 172.841 296.333 174.747 296.465 176.685C296.533 178.577 296.093 180.453 295.192 182.117C294.382 183.686 293.373 185.197 292.958 186.936C292.759 187.829 292.752 188.754 292.935 189.651C293.119 190.547 293.489 191.394 294.023 192.137C295.305 194.023 297.187 195.336 299.073 196.555C301.166 197.909 303.362 199.282 304.683 201.467C304.843 201.732 305.244 201.468 305.084 201.203C302.786 197.402 298.161 196.133 295.26 192.932C293.906 191.438 292.994 189.527 293.339 187.475C293.64 185.68 294.68 184.121 295.513 182.539C296.416 180.928 296.912 179.121 296.956 177.274C296.922 175.335 296.56 173.416 295.883 171.6C294.425 167.485 292.33 163.625 289.673 160.162C286.664 156.193 282.998 152.768 278.834 150.035C278.576 149.866 278.364 150.298 278.621 150.465Z"
        fill="white"
      />
      <path
        d="M294.294 168.264C295.633 167.532 296.669 166.347 297.217 164.921C297.764 163.494 297.786 161.92 297.281 160.479C297.177 160.187 296.72 160.334 296.824 160.626C297.297 161.967 297.276 163.434 296.764 164.761C296.251 166.088 295.282 167.188 294.03 167.863C293.759 168.01 294.023 168.41 294.294 168.264Z"
        fill="white"
      />
      <path
        d="M295.707 181.376C292.704 180.524 290.133 178.57 288.507 175.905C288.346 175.641 287.944 175.905 288.105 176.169C289.803 178.939 292.483 180.966 295.609 181.846C295.907 181.93 296.003 181.459 295.707 181.376Z"
        fill="white"
      />
      <path
        d="M286.351 156.747C285.892 157.139 285.343 157.41 284.753 157.537C284.163 157.663 283.55 157.641 282.971 157.472C282.674 157.385 282.579 157.856 282.873 157.943C283.514 158.126 284.19 158.15 284.843 158.011C285.495 157.873 286.103 157.577 286.615 157.149C286.666 157.113 286.702 157.058 286.715 156.996C286.728 156.935 286.716 156.87 286.684 156.816C286.648 156.763 286.594 156.726 286.532 156.714C286.469 156.701 286.405 156.713 286.351 156.747Z"
        fill="white"
      />
      <path
        d="M253.311 177.043C253.495 177.103 253.678 177.163 253.863 177.227C256.33 178.044 258.726 179.061 261.029 180.267C261.208 180.358 261.389 180.452 261.565 180.547C267.081 183.512 271.998 187.475 276.068 192.236C277.685 194.132 279.156 196.149 280.468 198.268C282.28 201.195 284.012 204.513 286.607 206.608C286.876 206.83 287.157 207.035 287.451 207.222L304.606 202.622C304.639 202.591 304.674 202.565 304.708 202.535L305.402 202.383C305.342 202.269 305.277 202.152 305.217 202.038C305.182 201.972 305.144 201.906 305.109 201.84C305.085 201.796 305.061 201.752 305.041 201.711C305.033 201.697 305.025 201.682 305.018 201.671C304.999 201.63 304.976 201.594 304.958 201.557C304.598 200.902 304.233 200.249 303.863 199.597C303.862 199.593 303.862 199.593 303.857 199.59C301.022 194.629 297.748 189.86 293.856 185.741C293.738 185.617 293.62 185.489 293.495 185.367C291.731 183.52 289.818 181.821 287.776 180.285C286.655 179.448 285.493 178.669 284.294 177.949C281.191 176.096 277.816 174.743 274.293 173.94C267.27 172.339 259.813 173.003 253.77 176.752C253.615 176.848 253.464 176.943 253.311 177.043Z"
        fill="#F2F2F2"
      />
      <path
        d="M253.408 177.251C262.405 176.658 271.36 178.917 279 183.707C280.664 184.709 282.159 185.967 283.43 187.435C284.623 188.905 285.401 190.667 285.682 192.539C285.98 194.28 286.083 196.095 286.798 197.732C287.177 198.565 287.727 199.309 288.413 199.914C289.099 200.519 289.905 200.972 290.778 201.244C292.936 201.977 295.228 201.891 297.468 201.729C299.954 201.549 302.533 201.322 304.902 202.271C305.189 202.386 305.351 201.933 305.064 201.818C300.942 200.168 296.486 201.941 292.243 201.133C290.264 200.756 288.386 199.78 287.426 197.934C286.587 196.319 286.479 194.448 286.193 192.683C285.945 190.852 285.253 189.111 284.177 187.609C282.984 186.082 281.54 184.768 279.906 183.725C276.267 181.318 272.271 179.499 268.067 178.334C263.276 176.978 258.288 176.452 253.319 176.779C253.012 176.799 253.102 177.271 253.408 177.251Z"
        fill="white"
      />
      <path
        d="M276.631 182.019C277.26 180.627 277.374 179.057 276.953 177.589C276.532 176.12 275.603 174.85 274.332 174.003C274.074 173.833 273.797 174.226 274.055 174.396C275.24 175.182 276.106 176.366 276.495 177.734C276.885 179.102 276.772 180.565 276.179 181.857C276.05 182.138 276.503 182.299 276.631 182.019Z"
        fill="white"
      />
      <path
        d="M285.647 191.637C282.737 192.765 279.509 192.754 276.607 191.607C276.32 191.493 276.158 191.945 276.445 192.059C279.468 193.248 282.827 193.252 285.853 192.071C286.141 191.959 285.934 191.525 285.647 191.637Z"
        fill="white"
      />
      <path
        d="M263.36 177.609C263.229 178.198 262.953 178.746 262.558 179.202C262.163 179.659 261.661 180.01 261.097 180.224C260.808 180.333 261.015 180.767 261.302 180.659C261.925 180.419 262.479 180.03 262.916 179.527C263.353 179.024 263.661 178.421 263.812 177.771C263.831 177.711 263.827 177.646 263.8 177.589C263.773 177.532 263.726 177.487 263.667 177.464C263.607 177.443 263.541 177.446 263.484 177.473C263.426 177.501 263.382 177.549 263.36 177.609Z"
        fill="white"
      />
      <path
        d="M223.383 204.025C225.612 204.025 227.419 202.216 227.419 199.986C227.419 197.755 225.612 195.947 223.383 195.947C221.154 195.947 219.347 197.755 219.347 199.986C219.347 202.216 221.154 204.025 223.383 204.025Z"
        fill="#FD6584"
      />
      <path
        d="M237.679 135.237C239.908 135.237 241.715 133.428 241.715 131.198C241.715 128.967 239.908 127.159 237.679 127.159C235.45 127.159 233.643 128.967 233.643 131.198C233.643 133.428 235.45 135.237 237.679 135.237Z"
        fill="#E6E6E6"
      />
      <path
        d="M162.581 23.2107C164.81 23.2107 166.617 21.4025 166.617 19.172C166.617 16.9415 164.81 15.1333 162.581 15.1333C160.352 15.1333 158.545 16.9415 158.545 19.172C158.545 21.4025 160.352 23.2107 162.581 23.2107Z"
        fill="#E6E6E6"
      />
      <path
        d="M78.0058 174.096C78.0058 175.124 78.0396 176.152 78.1115 177.172C78.1876 178.39 78.3186 179.6 78.4962 180.802C79.093 184.848 80.2335 188.794 81.8869 192.534C81.9715 192.72 82.056 192.906 82.1406 193.092L82.2674 193.371C82.3097 193.465 82.3562 193.558 82.3985 193.646C82.4577 193.769 82.5127 193.888 82.5719 194.006C82.6437 194.154 82.7156 194.302 82.7917 194.45C82.8002 194.467 82.8086 194.488 82.8213 194.51C82.8847 194.636 82.9439 194.759 83.0116 194.886C83.024 194.914 83.0381 194.941 83.0538 194.966C83.1257 195.11 83.2018 195.258 83.2779 195.402C83.3625 195.555 83.4428 195.711 83.5274 195.863C83.7303 196.24 83.9417 196.612 84.1573 196.985C84.2207 197.095 84.2841 197.205 84.3518 197.315C84.411 197.416 84.4702 197.518 84.5336 197.619C84.5759 197.687 84.6181 197.759 84.6604 197.826C84.7281 197.941 84.7957 198.051 84.8676 198.165C85.024 198.415 85.1847 198.664 85.3411 198.914H85.3453C85.4172 199.028 85.4933 199.142 85.5694 199.252V199.256C85.7935 199.595 86.026 199.933 86.2585 200.268L86.2602 200.268L86.2616 200.269L86.2625 200.27L86.2628 200.272C86.4488 200.543 86.6433 200.805 86.8335 201.067V201.071C86.9604 201.236 87.083 201.406 87.2098 201.571C87.3113 201.706 87.417 201.846 87.5227 201.981C87.5438 202.011 87.5692 202.036 87.5903 202.066V202.07C87.6199 202.108 87.6495 202.142 87.6791 202.18C88.1272 202.755 88.5895 203.318 89.0658 203.868L89.1419 203.957C89.1673 203.986 89.1969 204.016 89.2223 204.05C89.2645 204.096 89.3026 204.143 89.3449 204.19C89.3829 204.236 89.4252 204.283 89.4675 204.329C91.9505 207.128 94.766 209.613 97.8513 211.729C98.2614 212.012 98.68 212.287 99.0985 212.554C99.2042 212.621 99.3099 212.689 99.4156 212.753C99.4452 212.774 99.479 212.795 99.5086 212.816C99.5678 212.85 99.6228 212.884 99.682 212.922C99.7369 212.956 99.7919 212.99 99.8511 213.023C100.075 213.163 100.303 213.298 100.532 213.434C100.587 213.468 100.646 213.501 100.705 213.535C100.76 213.565 100.819 213.599 100.878 213.633C100.878 213.637 100.878 213.637 100.883 213.637H100.887V213.641H100.891C101.174 213.806 101.462 213.967 101.754 214.123C101.91 214.208 102.071 214.297 102.231 214.377C102.595 214.576 102.963 214.766 103.331 214.948C103.462 215.012 103.597 215.075 103.728 215.143C103.838 215.194 103.948 215.249 104.058 215.3C104.092 215.316 104.121 215.329 104.151 215.346C104.244 215.388 104.333 215.431 104.426 215.473C104.527 215.52 104.629 215.566 104.726 215.613C104.84 215.663 104.95 215.714 105.064 215.761C105.487 215.951 105.914 216.133 106.341 216.306C107.871 216.934 109.435 217.477 111.025 217.931C111.097 217.952 111.173 217.973 111.249 217.995C111.93 218.185 112.615 218.358 113.304 218.515C114.509 218.794 115.725 219.023 116.953 219.2H116.961C117.109 219.221 117.253 219.243 117.401 219.264C117.414 219.264 117.426 219.268 117.443 219.268C117.443 219.272 117.443 219.272 117.447 219.268C117.608 219.289 117.773 219.31 117.934 219.331C117.945 219.335 117.956 219.336 117.967 219.336C118.12 219.357 118.276 219.374 118.428 219.391C118.593 219.408 118.758 219.425 118.923 219.441C119.05 219.454 119.177 219.467 119.303 219.48C119.314 219.483 119.326 219.485 119.337 219.484C119.451 219.492 119.566 219.505 119.68 219.513H119.697C119.794 219.522 119.895 219.53 119.993 219.539C121.172 219.632 122.356 219.674 123.557 219.674C124.855 219.674 126.144 219.623 127.417 219.513H127.429C127.704 219.488 127.975 219.463 128.245 219.437C128.317 219.429 128.385 219.425 128.457 219.416C128.469 219.416 128.486 219.412 128.499 219.412C128.52 219.408 128.541 219.408 128.567 219.403C128.578 219.404 128.589 219.402 128.6 219.399C128.693 219.391 128.786 219.378 128.884 219.37L129.201 219.331C129.307 219.319 129.412 219.302 129.518 219.289C129.543 219.285 129.564 219.285 129.59 219.281C129.666 219.272 129.742 219.26 129.818 219.247C129.856 219.243 129.894 219.238 129.928 219.234C130.004 219.221 130.076 219.213 130.152 219.2H130.156C132.476 218.866 134.766 218.35 137.005 217.656C137.124 217.618 137.242 217.584 137.361 217.546C137.365 217.546 137.369 217.542 137.373 217.542C137.407 217.529 137.445 217.521 137.479 217.508C137.796 217.406 138.109 217.301 138.422 217.191C138.853 217.047 139.28 216.89 139.707 216.73C139.821 216.683 139.935 216.641 140.049 216.594C140.282 216.505 140.51 216.412 140.739 216.319C141.128 216.163 141.517 215.998 141.901 215.828C142.261 215.668 142.616 215.507 142.971 215.338C142.979 215.333 142.992 215.333 143.001 215.325C143.081 215.287 143.161 215.249 143.237 215.211H143.241C145.188 214.276 147.066 213.203 148.86 212C149.435 211.615 150.002 211.217 150.56 210.807C151.008 210.472 151.448 210.134 151.887 209.787C154.057 208.06 156.065 206.14 157.887 204.05V204.046C158.018 203.902 158.14 203.754 158.267 203.606C158.288 203.585 158.305 203.563 158.322 203.546C158.403 203.449 158.479 203.356 158.559 203.263V203.259C158.665 203.136 158.77 203.009 158.872 202.882C158.897 202.853 158.918 202.827 158.939 202.802V202.798C159.024 202.696 159.104 202.595 159.189 202.489C159.231 202.438 159.269 202.387 159.312 202.336V202.332C159.561 202.019 159.802 201.706 160.043 201.385C160.106 201.3 160.17 201.211 160.237 201.122C160.542 200.712 160.838 200.293 161.125 199.874V199.87C161.235 199.713 161.341 199.553 161.451 199.396C161.557 199.235 161.662 199.075 161.768 198.914C161.874 198.745 161.984 198.58 162.089 198.41L162.102 198.385C162.208 198.224 162.313 198.055 162.415 197.89C162.495 197.755 162.575 197.623 162.656 197.492C162.707 197.403 162.761 197.315 162.812 197.222C162.832 197.193 162.85 197.163 162.867 197.133C162.986 196.925 163.104 196.718 163.222 196.511C163.362 196.27 163.497 196.024 163.632 195.779C163.683 195.677 163.738 195.576 163.793 195.474C163.814 195.44 163.831 195.407 163.848 195.373C163.911 195.254 163.975 195.132 164.034 195.009C164.097 194.89 164.161 194.768 164.224 194.645C164.283 194.527 164.347 194.404 164.406 194.281C164.453 194.188 164.495 194.095 164.541 194.002C164.545 193.994 164.549 193.987 164.554 193.981C164.575 193.934 164.596 193.892 164.613 193.85C164.618 193.843 164.623 193.836 164.626 193.828C164.672 193.735 164.715 193.638 164.761 193.545C164.82 193.422 164.875 193.295 164.935 193.173C164.973 193.088 165.015 192.999 165.053 192.91C165.108 192.788 165.167 192.665 165.218 192.538C165.277 192.411 165.332 192.284 165.387 192.153C165.403 192.121 165.417 192.089 165.429 192.056C165.459 191.997 165.48 191.937 165.505 191.878C165.573 191.722 165.641 191.561 165.704 191.404C165.78 191.227 165.852 191.045 165.92 190.867C165.987 190.702 166.051 190.533 166.114 190.363L166.14 190.304C167.125 187.708 167.872 185.027 168.372 182.295V182.291C168.654 180.732 168.856 179.158 168.976 177.578C168.981 177.489 168.989 177.405 168.993 177.32C169.069 176.25 169.103 175.175 169.103 174.096C169.103 173.618 169.095 173.144 169.082 172.67C168.787 162.76 165.256 153.22 159.028 145.509C158.893 145.34 158.758 145.17 158.618 145.001C158.479 144.832 158.335 144.667 158.195 144.502C153.561 139.071 147.716 134.805 141.131 132.049C134.547 129.293 127.407 128.124 120.288 128.635C113.169 129.147 106.27 131.325 100.147 134.995C94.024 138.664 88.8481 143.722 85.0367 149.761C84.8295 150.091 84.6224 150.425 84.4237 150.759C84.2207 151.098 84.0263 151.436 83.8318 151.779C80.0016 158.592 77.9948 166.279 78.0058 174.096Z"
        fill="#F2F2F2"
      />
      <path
        d="M121.746 144.283C129.236 146.207 136.912 147.315 144.639 147.59C145.808 147.654 146.979 147.646 148.147 147.566C150.713 147.35 152.91 146.543 155.358 146.102C156.535 145.891 157.846 145.736 159.03 145.508C158.757 145.168 158.478 144.832 158.194 144.5C155.64 144.716 153.046 145.423 150.46 145.752C146.066 146.311 141.142 145.687 137.811 144.148C136.194 143.401 134.956 142.469 133.345 141.719C133.126 141.607 132.889 141.534 132.645 141.504C132.18 141.49 131.722 141.609 131.323 141.848C128.62 143.213 124.697 144.322 121.056 143.712C120.784 143.924 121.3 144.166 121.746 144.283Z"
        fill="white"
      />
      <path
        d="M105.632 149.087C105.599 149.081 105.565 149.076 105.532 149.071C105.672 148.984 105.804 148.885 105.926 148.774L105.632 149.087Z"
        fill="white"
      />
      <path
        d="M83.8328 151.78C85.3537 151.902 86.8825 151.993 88.4193 152.052C89.5879 152.115 90.7592 152.107 91.9268 152.028C94.4927 151.811 96.6906 151.005 99.1383 150.564C101.382 150.161 104.108 149.966 105.532 149.071C101.851 148.433 98.0366 149.731 94.2406 150.214C91.17 150.592 88.0574 150.438 85.039 149.759C84.6188 150.421 84.2167 151.095 83.8328 151.78Z"
        fill="white"
      />
      <path
        d="M102.231 214.377C102.595 214.576 102.963 214.766 103.331 214.948C103.462 215.012 103.597 215.075 103.728 215.143C103.838 215.194 103.948 215.249 104.058 215.299C104.092 215.316 104.121 215.329 104.151 215.346C104.244 215.388 104.333 215.431 104.426 215.473C104.527 215.52 104.629 215.566 104.726 215.613C104.84 215.663 104.95 215.714 105.064 215.761C105.487 215.951 105.914 216.133 106.341 216.306C107.871 216.934 109.435 217.476 111.025 217.931C111.097 217.952 111.173 217.973 111.249 217.994C111.93 218.185 112.615 218.358 113.304 218.515C114.509 218.794 115.725 219.023 116.953 219.2H116.961C117.109 219.221 117.253 219.243 117.401 219.264C117.414 219.264 117.426 219.268 117.443 219.268C117.443 219.272 117.443 219.272 117.447 219.268C117.608 219.289 117.773 219.31 117.934 219.331C117.945 219.335 117.956 219.336 117.968 219.336C118.12 219.357 118.276 219.374 118.428 219.391C118.593 219.408 118.758 219.424 118.923 219.441C119.05 219.454 119.177 219.467 119.304 219.479C119.314 219.483 119.326 219.485 119.337 219.484C119.451 219.492 119.566 219.505 119.68 219.513H119.697C119.794 219.522 119.895 219.53 119.993 219.539C121.172 219.632 122.356 219.674 123.557 219.674C124.855 219.674 126.144 219.623 127.417 219.513H127.429C127.704 219.488 127.975 219.463 128.245 219.437C128.317 219.429 128.385 219.424 128.457 219.416C128.469 219.416 128.486 219.412 128.499 219.412C128.52 219.408 128.541 219.408 128.567 219.403C128.578 219.404 128.59 219.402 128.601 219.399C128.694 219.391 128.787 219.378 128.884 219.369L129.201 219.331C129.307 219.319 129.412 219.302 129.518 219.289C129.543 219.285 129.565 219.285 129.59 219.281C129.666 219.272 129.742 219.259 129.818 219.247C129.856 219.243 129.894 219.238 129.928 219.234C130.004 219.221 130.076 219.213 130.152 219.2H130.156C132.476 218.866 134.766 218.35 137.006 217.656C137.124 217.618 137.242 217.584 137.361 217.546C136.815 214.998 135.691 212.61 134.076 210.567C132.461 208.523 130.398 206.878 128.047 205.759C128.628 205.205 129.03 204.488 129.201 203.703C129.311 203.197 129.319 202.674 129.227 202.164C129.134 201.654 128.941 201.168 128.66 200.733C128.111 199.89 127.25 199.299 126.267 199.091L120.001 197.78C118.992 197.583 117.947 197.793 117.092 198.363C116.237 198.934 115.641 199.819 115.435 200.826C115.371 201.135 115.343 201.45 115.35 201.765C109.317 205.784 104.975 209.952 102.231 214.377Z"
        fill="#6D11D2"
      />
      <path
        d="M120.732 195.303C127.941 195.303 133.786 189.455 133.786 182.241C133.786 175.026 127.941 169.178 120.732 169.178C113.523 169.178 107.678 175.026 107.678 182.241C107.678 189.455 113.523 195.303 120.732 195.303Z"
        fill="#FFB8B8"
      />
      <path
        d="M112.595 193.503C111.203 192.551 109.782 190.723 108.833 189.352C107.575 187.551 106.71 185.505 106.293 183.348C105.876 181.19 105.917 178.969 106.413 176.829C106.571 175.973 106.959 175.176 107.536 174.525C108.151 173.916 109.155 173.628 109.906 174.058C109.701 173.509 109.611 172.924 109.641 172.339C109.67 171.754 109.82 171.182 110.08 170.657C110.607 169.608 111.371 168.698 112.312 167.996C114.11 166.611 116.25 165.738 118.504 165.471C120.758 165.204 123.043 165.552 125.115 166.48C125.93 166.858 124.591 163.938 125.445 164.219C126.292 164.548 127.235 164.531 128.071 164.173C128.873 163.769 131.534 166.171 131.226 165.326C131.835 165.603 132.365 166.029 132.765 166.566C133.165 167.103 133.423 167.732 133.514 168.395C133.606 169.058 133.528 169.734 133.289 170.359C133.049 170.984 132.656 171.538 132.144 171.97C131.471 172.539 130.63 172.867 129.892 173.35C128.6 174.26 127.685 175.612 127.32 177.15C126.956 178.689 127.167 180.308 127.914 181.702C127.138 181.203 126.286 180.832 125.392 180.605C124.943 180.495 124.475 180.495 124.026 180.603C123.577 180.711 123.161 180.924 122.811 181.226C122.519 181.561 122.298 181.952 122.162 182.375C122.027 182.798 121.979 183.244 122.022 183.687C122.051 184.573 122.262 185.446 122.285 186.333C122.355 188.935 123.747 193.881 121.731 195.264C120.141 196.355 114.496 194.802 112.595 193.503Z"
        fill="#2F2E41"
      />
      <path
        d="M129.707 116.858C129.433 116.858 129.16 116.803 128.907 116.696C128.528 116.544 128.205 116.281 127.978 115.942C127.751 115.602 127.633 115.203 127.637 114.794V112.727C127.636 112.293 127.464 111.877 127.158 111.571C126.851 111.264 126.436 111.091 126.002 111.091C125.457 111.09 124.934 110.873 124.548 110.487C124.162 110.101 123.945 109.578 123.945 109.032V80.7047C123.945 80.1589 124.162 79.6355 124.548 79.2495C124.934 78.8635 125.457 78.6464 126.002 78.6458H219.787C220.333 78.6464 220.856 78.8635 221.241 79.2495C221.627 79.6355 221.844 80.1588 221.845 80.7047V109.032C221.844 109.578 221.627 110.101 221.241 110.487C220.856 110.873 220.333 111.09 219.787 111.091H136.982C136.768 111.09 136.555 111.132 136.356 111.215C136.158 111.297 135.978 111.418 135.826 111.57L131.149 116.25C130.961 116.442 130.737 116.594 130.489 116.699C130.242 116.803 129.976 116.857 129.707 116.858Z"
        fill="white"
      />
      <path
        d="M129.707 117.281C129.377 117.281 129.05 117.215 128.745 117.087C128.289 116.904 127.899 116.586 127.625 116.177C127.352 115.768 127.209 115.286 127.214 114.794V112.727C127.214 112.405 127.086 112.097 126.859 111.87C126.632 111.642 126.324 111.514 126.002 111.514C125.345 111.513 124.714 111.251 124.249 110.786C123.784 110.321 123.523 109.69 123.522 109.032V80.7047C123.523 80.0467 123.784 79.4158 124.249 78.9505C124.714 78.4852 125.345 78.2234 126.002 78.2227H219.787C220.445 78.2234 221.075 78.4852 221.54 78.9505C222.005 79.4158 222.267 80.0467 222.267 80.7047V109.032C222.267 109.69 222.005 110.321 221.54 110.786C221.075 111.251 220.445 111.513 219.787 111.514H136.982C136.823 111.513 136.665 111.545 136.518 111.606C136.371 111.667 136.237 111.756 136.125 111.869L131.448 116.549C131.221 116.78 130.95 116.964 130.651 117.09C130.352 117.215 130.032 117.281 129.707 117.281ZM126.002 79.0688C125.569 79.0693 125.153 79.2419 124.847 79.5485C124.54 79.8552 124.368 80.271 124.368 80.7047V109.032C124.368 109.465 124.54 109.881 124.847 110.188C125.153 110.495 125.569 110.667 126.002 110.668C126.548 110.668 127.071 110.886 127.456 111.272C127.842 111.658 128.059 112.181 128.06 112.727V114.794C128.06 115.118 128.156 115.434 128.335 115.703C128.515 115.972 128.77 116.182 129.069 116.305C129.368 116.429 129.696 116.462 130.013 116.399C130.33 116.335 130.622 116.18 130.85 115.951L135.527 111.271C135.718 111.079 135.945 110.927 136.194 110.824C136.444 110.72 136.712 110.667 136.982 110.668H219.787C220.221 110.667 220.636 110.495 220.943 110.188C221.249 109.881 221.421 109.465 221.422 109.032V80.7047C221.421 80.271 221.249 79.8552 220.943 79.5485C220.636 79.2418 220.22 79.0693 219.787 79.0688L126.002 79.0688Z"
        fill="#3F3D56"
      />
      <path
        d="M211.728 91.4701H135.678C135.387 91.4701 135.109 91.3546 134.903 91.149C134.698 90.9435 134.583 90.6647 134.583 90.3739C134.583 90.0832 134.698 89.8044 134.903 89.5989C135.109 89.3933 135.387 89.2778 135.678 89.2778H211.728C212.018 89.2778 212.297 89.3933 212.502 89.5989C212.708 89.8044 212.823 90.0832 212.823 90.3739C212.823 90.6647 212.708 90.9435 212.502 91.149C212.297 91.3546 212.018 91.4701 211.728 91.4701Z"
        fill="#6D11D2"
      />
      <path
        d="M211.588 96.1268H135.678C135.387 96.1268 135.109 96.0113 134.903 95.8058C134.698 95.6002 134.583 95.3214 134.583 95.0307C134.583 94.74 134.698 94.4612 134.903 94.2556C135.109 94.0501 135.387 93.9346 135.678 93.9346H211.588C211.879 93.9346 212.157 94.0501 212.363 94.2556C212.568 94.4612 212.683 94.74 212.683 95.0307C212.683 95.3214 212.568 95.6002 212.363 95.8058C212.157 96.0113 211.879 96.1268 211.588 96.1268Z"
        fill="#E6E6E6"
      />
      <path
        d="M211.513 100.784H135.678C135.387 100.784 135.109 100.668 134.903 100.462C134.698 100.257 134.583 99.9781 134.583 99.6874C134.583 99.3967 134.698 99.1179 134.903 98.9123C135.109 98.7068 135.387 98.5913 135.678 98.5913H211.513C211.803 98.5913 212.082 98.7068 212.287 98.9123C212.493 99.1179 212.608 99.3967 212.608 99.6874C212.608 99.9781 212.493 100.257 212.287 100.462C212.082 100.668 211.803 100.784 211.513 100.784Z"
        fill="#E6E6E6"
      />
      <path
        d="M331.491 74.2499C331.491 75.278 331.457 76.3061 331.385 77.3257C331.309 78.5441 331.178 79.7542 331 80.9557C330.404 85.0014 329.263 88.9477 327.61 92.6876C327.525 92.8738 327.441 93.0599 327.356 93.2461L327.229 93.5253C327.187 93.6184 327.14 93.7115 327.098 93.8003C327.039 93.923 326.984 94.0415 326.925 94.1599C326.853 94.308 326.781 94.4561 326.705 94.6041C326.696 94.6211 326.688 94.6422 326.675 94.6634C326.612 94.7903 326.553 94.913 326.485 95.0399C326.473 95.0676 326.458 95.0944 326.443 95.1203C326.371 95.2641 326.295 95.4122 326.219 95.5561C326.134 95.7084 326.054 95.8649 325.969 96.0172C325.766 96.3938 325.555 96.7661 325.339 97.1384C325.276 97.2484 325.212 97.3584 325.145 97.4684C325.086 97.5699 325.026 97.6715 324.963 97.773C324.921 97.8407 324.878 97.9126 324.836 97.9803C324.768 98.0945 324.701 98.2045 324.629 98.3188C324.473 98.5684 324.312 98.818 324.155 99.0676H324.151C324.079 99.1818 324.003 99.2961 323.927 99.4061V99.4103C323.703 99.7488 323.471 100.087 323.238 100.421C323.237 100.421 323.237 100.422 323.236 100.422C323.236 100.422 323.235 100.422 323.235 100.423C323.235 100.423 323.234 100.424 323.234 100.424C323.234 100.425 323.234 100.425 323.234 100.426C323.048 100.696 322.853 100.959 322.663 101.221V101.225C322.536 101.39 322.414 101.56 322.287 101.725C322.185 101.86 322.08 102 321.974 102.135C321.953 102.164 321.927 102.19 321.906 102.22V102.224C321.877 102.262 321.847 102.296 321.817 102.334C321.369 102.909 320.907 103.472 320.431 104.022L320.355 104.111C320.329 104.14 320.3 104.17 320.274 104.204C320.232 104.25 320.194 104.297 320.152 104.343C320.114 104.39 320.071 104.436 320.029 104.483C317.546 107.282 314.731 109.767 311.645 111.883C311.235 112.166 310.817 112.441 310.398 112.708C310.292 112.775 310.187 112.843 310.081 112.906C310.051 112.928 310.018 112.949 309.988 112.97C309.929 113.004 309.874 113.038 309.815 113.076C309.76 113.11 309.705 113.143 309.645 113.177C309.421 113.317 309.193 113.452 308.965 113.588C308.91 113.621 308.851 113.655 308.791 113.689C308.736 113.719 308.677 113.753 308.618 113.786C308.618 113.791 308.618 113.791 308.614 113.791H308.61V113.795H308.605C308.322 113.96 308.035 114.121 307.743 114.277C307.587 114.362 307.426 114.451 307.265 114.531C306.902 114.73 306.534 114.92 306.166 115.102C306.035 115.166 305.9 115.229 305.769 115.297C305.659 115.348 305.549 115.403 305.439 115.453C305.405 115.47 305.375 115.483 305.346 115.5C305.253 115.542 305.164 115.585 305.071 115.627C304.969 115.673 304.868 115.72 304.771 115.766C304.657 115.817 304.547 115.868 304.433 115.915C304.01 116.105 303.583 116.287 303.156 116.46C301.625 117.088 300.062 117.63 298.471 118.085C298.399 118.106 298.323 118.127 298.247 118.148C297.566 118.339 296.882 118.512 296.192 118.669C294.988 118.948 293.771 119.176 292.544 119.354H292.535C292.387 119.375 292.244 119.396 292.096 119.418C292.083 119.418 292.07 119.422 292.053 119.422C292.053 119.426 292.053 119.426 292.049 119.422C291.888 119.443 291.724 119.464 291.563 119.485C291.552 119.489 291.541 119.49 291.529 119.49C291.377 119.511 291.22 119.528 291.068 119.545C290.903 119.561 290.739 119.578 290.574 119.595C290.447 119.608 290.32 119.621 290.193 119.633C290.182 119.637 290.171 119.639 290.159 119.638C290.045 119.646 289.931 119.659 289.817 119.667H289.8C289.703 119.676 289.601 119.684 289.504 119.693C288.324 119.786 287.141 119.828 285.94 119.828C284.642 119.828 283.352 119.777 282.08 119.667H282.067C281.792 119.642 281.522 119.616 281.251 119.591C281.179 119.583 281.112 119.578 281.04 119.57C281.027 119.57 281.01 119.566 280.998 119.566C280.976 119.561 280.955 119.561 280.93 119.557C280.918 119.558 280.907 119.556 280.896 119.553C280.803 119.545 280.71 119.532 280.613 119.523L280.296 119.485C280.19 119.473 280.084 119.456 279.979 119.443C279.953 119.439 279.932 119.439 279.907 119.435C279.831 119.426 279.755 119.413 279.678 119.401C279.64 119.396 279.602 119.392 279.569 119.388C279.492 119.375 279.421 119.367 279.344 119.354H279.34C277.02 119.02 274.73 118.504 272.491 117.81C272.373 117.772 272.254 117.738 272.136 117.7C272.132 117.7 272.128 117.696 272.123 117.696C272.089 117.683 272.051 117.675 272.018 117.662C271.701 117.56 271.388 117.455 271.075 117.345C270.644 117.201 270.217 117.044 269.79 116.883C269.675 116.837 269.561 116.795 269.447 116.748C269.215 116.659 268.986 116.566 268.758 116.473C268.369 116.316 267.98 116.151 267.595 115.982C267.236 115.821 266.881 115.661 266.526 115.491C266.517 115.487 266.504 115.487 266.496 115.479C266.416 115.441 266.335 115.403 266.259 115.365H266.255C264.308 114.429 262.43 113.356 260.636 112.153C260.061 111.768 259.495 111.371 258.937 110.96C258.488 110.626 258.049 110.288 257.609 109.941C255.44 108.214 253.432 106.294 251.61 104.204V104.2C251.479 104.056 251.356 103.908 251.229 103.76C251.208 103.738 251.191 103.717 251.174 103.7C251.094 103.603 251.018 103.51 250.938 103.417V103.413C250.832 103.29 250.726 103.163 250.625 103.036C250.599 103.006 250.578 102.981 250.557 102.956V102.951C250.472 102.85 250.392 102.748 250.308 102.643C250.265 102.592 250.227 102.541 250.185 102.49V102.486C249.936 102.173 249.695 101.86 249.454 101.538C249.39 101.454 249.327 101.365 249.259 101.276C248.955 100.866 248.659 100.447 248.371 100.028V100.024C248.261 99.8672 248.156 99.7065 248.046 99.5499C247.94 99.3892 247.834 99.2284 247.729 99.0676C247.623 98.8984 247.513 98.7334 247.407 98.5642L247.395 98.5388C247.289 98.378 247.183 98.2088 247.082 98.0438C247.001 97.9084 246.921 97.7772 246.841 97.6461C246.79 97.5572 246.735 97.4684 246.684 97.3753C246.665 97.3467 246.646 97.317 246.629 97.2865C246.511 97.0792 246.393 96.8719 246.274 96.6646C246.135 96.4234 245.999 96.178 245.864 95.9326C245.813 95.8311 245.758 95.7296 245.703 95.628C245.682 95.5942 245.665 95.5603 245.649 95.5265C245.585 95.408 245.522 95.2854 245.462 95.1626C245.399 95.0442 245.336 94.9215 245.272 94.7988C245.213 94.6803 245.15 94.5576 245.09 94.4349C245.044 94.3419 245.002 94.2488 244.955 94.1557C244.952 94.1481 244.948 94.141 244.942 94.1346C244.921 94.088 244.9 94.0457 244.883 94.0034C244.878 93.997 244.874 93.9898 244.871 93.9822C244.824 93.8892 244.782 93.7919 244.735 93.6988C244.676 93.5761 244.621 93.4492 244.562 93.3265C244.524 93.2419 244.482 93.153 244.444 93.0642C244.389 92.9415 244.329 92.8188 244.279 92.6919C244.22 92.5649 244.165 92.438 244.11 92.3069C244.094 92.2752 244.08 92.2427 244.067 92.2096C244.038 92.1503 244.017 92.0911 243.991 92.0319C243.924 91.8753 243.856 91.7146 243.792 91.558C243.716 91.3803 243.645 91.1984 243.577 91.0207C243.509 90.8557 243.446 90.6865 243.382 90.5172L243.357 90.458C242.372 87.8616 241.625 85.181 241.125 82.4492V82.4449C240.843 80.8853 240.641 79.3122 240.52 77.7319C240.516 77.643 240.507 77.5584 240.503 77.4738C240.427 76.4034 240.393 75.3288 240.393 74.2499C240.393 73.7719 240.402 73.298 240.414 72.8242C240.71 62.9142 244.241 53.3738 250.468 45.6626C250.604 45.4934 250.739 45.3242 250.878 45.1549C251.018 44.9857 251.162 44.8207 251.301 44.6557C255.936 39.2243 261.781 34.9587 268.365 32.2026C274.95 29.4466 282.089 28.2773 289.208 28.789C296.327 29.3008 303.226 31.4792 309.349 35.1487C315.473 38.8181 320.648 43.876 324.46 49.9145C324.667 50.2445 324.874 50.5788 325.073 50.913C325.276 51.2515 325.47 51.59 325.665 51.9326C329.495 58.7455 331.502 66.4329 331.491 74.2499Z"
        fill="#F2F2F2"
      />
      <path
        d="M287.75 44.4371C280.26 46.3604 272.585 47.4689 264.857 47.7436C263.688 47.8073 262.517 47.7993 261.35 47.7197C258.784 47.5038 256.586 46.6968 254.138 46.2558C252.961 46.0444 251.651 45.8898 250.466 45.6613C250.74 45.3214 251.018 44.9858 251.302 44.6542C253.856 44.8702 256.451 45.5772 259.036 45.9057C263.43 46.4649 268.354 45.8409 271.686 44.3019C273.302 43.5551 274.54 42.6231 276.151 41.8729C276.37 41.7604 276.607 41.6876 276.852 41.6581C277.316 41.6441 277.775 41.7632 278.174 42.0013C280.876 43.3664 284.799 44.4758 288.441 43.8654C288.712 44.078 288.196 44.3201 287.75 44.4371Z"
        fill="white"
      />
      <path
        d="M303.864 49.2413C303.898 49.2352 303.931 49.2304 303.964 49.2247C303.824 49.1379 303.693 49.0386 303.571 48.928L303.864 49.2413Z"
        fill="white"
      />
      <path
        d="M325.664 51.9344C324.143 52.056 322.614 52.1466 321.077 52.2061C319.909 52.2692 318.737 52.2612 317.57 52.1822C315.004 51.9651 312.806 51.1592 310.358 50.7182C308.115 50.3147 305.389 50.1204 303.965 49.2247C307.646 48.587 311.46 49.8851 315.256 50.3681C318.327 50.746 321.439 50.5922 324.458 49.9135C324.878 50.575 325.28 51.2487 325.664 51.9344Z"
        fill="white"
      />
      <path
        d="M280.805 94.1943C288.361 94.1943 294.486 87.9284 294.486 80.1991C294.486 72.4697 288.361 66.2039 280.805 66.2039C273.249 66.2039 267.124 72.4697 267.124 80.1991C267.124 87.9284 273.249 94.1943 280.805 94.1943Z"
        fill="#FFB6B6"
      />
      <path
        d="M307.265 114.531C306.901 114.73 306.534 114.92 306.166 115.102C306.035 115.166 305.899 115.229 305.768 115.297C305.658 115.348 305.548 115.403 305.439 115.453C305.405 115.47 305.375 115.483 305.346 115.5C305.252 115.542 305.164 115.584 305.071 115.627C304.969 115.673 304.868 115.72 304.771 115.766C304.656 115.817 304.546 115.868 304.432 115.914C304.01 116.105 303.583 116.287 303.155 116.46C301.625 117.088 300.061 117.63 298.471 118.085C298.399 118.106 298.323 118.127 298.247 118.148C297.566 118.339 296.881 118.512 296.192 118.669C294.987 118.948 293.771 119.176 292.544 119.354H292.535C292.387 119.375 292.243 119.396 292.095 119.418C292.083 119.418 292.07 119.422 292.053 119.422C292.053 119.426 292.053 119.426 292.049 119.422C291.888 119.443 291.723 119.464 291.563 119.485C291.552 119.488 291.54 119.49 291.529 119.489C291.377 119.511 291.22 119.528 291.068 119.544C290.903 119.561 290.738 119.578 290.573 119.595C290.447 119.608 290.32 119.621 290.193 119.633C290.182 119.637 290.171 119.638 290.159 119.638C290.045 119.646 289.931 119.659 289.817 119.667H289.8C289.702 119.676 289.601 119.684 289.504 119.693C288.324 119.786 287.14 119.828 285.94 119.828C284.642 119.828 283.352 119.777 282.08 119.667H282.067C281.792 119.642 281.522 119.616 281.251 119.591C281.179 119.583 281.111 119.578 281.04 119.57C281.027 119.57 281.01 119.566 280.997 119.566C280.976 119.561 280.955 119.561 280.93 119.557C280.918 119.558 280.907 119.556 280.896 119.553C280.803 119.544 280.71 119.532 280.613 119.523L280.295 119.485C280.19 119.473 280.084 119.456 279.978 119.443C279.953 119.439 279.932 119.439 279.907 119.434C279.83 119.426 279.754 119.413 279.678 119.401C279.64 119.396 279.602 119.392 279.568 119.388C279.492 119.375 279.42 119.367 279.344 119.354H279.34C277.02 119.02 274.73 118.504 272.491 117.81C272.372 117.772 272.254 117.738 272.136 117.7C272.682 115.152 273.805 112.764 275.42 110.721C277.035 108.677 279.098 107.032 281.45 105.913C280.868 105.358 280.466 104.642 280.295 103.857C280.186 103.35 280.177 102.827 280.27 102.318C280.363 101.808 280.555 101.322 280.837 100.887C281.385 100.044 282.246 99.4533 283.23 99.2452L289.495 97.9337C290.504 97.7369 291.549 97.9465 292.404 98.5169C293.259 99.0874 293.855 99.9725 294.061 100.98C294.125 101.289 294.154 101.604 294.146 101.919C300.179 105.938 304.521 110.106 307.265 114.531Z"
        fill="#6D11D2"
      />
      <path
        d="M317.413 74.1294C315.088 71.4157 313.982 67.8153 312.123 64.7337C310.264 61.6522 307.029 58.8942 303.611 59.6113C301.255 60.1057 299.435 62.186 298.513 64.5053C297.591 66.8246 297.419 69.3824 297.257 71.8881C297.314 66.7387 293.924 61.7468 289.254 60.1037C284.584 58.4606 278.995 60.2934 276.061 64.4303C274.78 63.2426 273.109 62.5663 271.363 62.5296C269.617 62.4929 267.919 63.0984 266.59 64.2313C263.965 66.5789 263.384 71.047 265.315 74.0414C267.245 77.0357 271.425 78.1512 274.493 76.4905L275.578 77.4888C274.967 79.6254 276.151 82.1493 278.141 82.9536C279.755 83.6058 282.033 83.4973 282.637 85.1919C283.059 86.376 282.27 87.6399 281.431 88.5498C280.592 89.4597 279.596 90.3993 279.495 91.6584C279.343 93.5582 281.305 94.889 283.088 95.2898C287.389 96.2563 292.16 94.2636 294.639 90.4659C294.686 90.3926 294.738 90.3193 294.795 90.2459C296.185 88.4663 297.199 86.4217 297.773 84.237C298.347 82.0523 298.47 79.7733 298.135 77.5394C297.995 76.6617 298.136 76.0088 298.741 75.8009C301.354 74.9024 308.678 76.7882 310.894 78.4925C313.111 80.1968 314.886 82.4523 317.02 84.2666C319.155 86.0809 321.876 87.4804 324.587 86.9863C326.758 86.5906 328.604 85.0134 329.847 83.1109C331.089 81.2085 331.804 78.9909 332.444 76.7871C327.644 79.5623 321.073 78.4006 317.413 74.1294Z"
        fill="#2F2E41"
      />
      <path
        d="M36.5729 45.9037C36.5729 46.9318 36.6067 47.9599 36.6786 48.9795C36.7547 50.1979 36.8858 51.408 37.0634 52.6095C37.6601 56.6552 38.8007 60.6015 40.4541 64.3414C40.5386 64.5276 40.6232 64.7137 40.7078 64.8999L40.8346 65.1791C40.8769 65.2722 40.9234 65.3653 40.9656 65.4541C41.0248 65.5768 41.0798 65.6953 41.139 65.8137C41.2109 65.9618 41.2827 66.1099 41.3588 66.2579C41.3673 66.2749 41.3758 66.296 41.3884 66.3172C41.4519 66.4441 41.511 66.5668 41.5787 66.6937C41.5911 66.7214 41.6052 66.7482 41.621 66.7741C41.6928 66.918 41.7689 67.066 41.845 67.2099C41.9296 67.3622 42.0099 67.5187 42.0945 67.671C42.2974 68.0476 42.5088 68.4199 42.7244 68.7922C42.7879 68.9022 42.8513 69.0122 42.9189 69.1222C42.9781 69.2237 43.0373 69.3253 43.1007 69.4268C43.143 69.4945 43.1853 69.5664 43.2276 69.6341C43.2952 69.7483 43.3628 69.8583 43.4347 69.9726C43.5911 70.2222 43.7518 70.4718 43.9082 70.7214H43.9125C43.9843 70.8356 44.0604 70.9499 44.1365 71.0599V71.0641C44.3606 71.4026 44.5931 71.741 44.8257 72.0752C44.8262 72.0752 44.8268 72.0753 44.8273 72.0755C44.8278 72.0758 44.8283 72.0761 44.8287 72.0765C44.8291 72.0769 44.8294 72.0773 44.8296 72.0778C44.8298 72.0784 44.8299 72.0789 44.8299 72.0795C45.0159 72.3502 45.2104 72.6125 45.4007 72.8749V72.8791C45.5275 73.0441 45.6501 73.2133 45.7769 73.3783C45.8784 73.5137 45.9841 73.6533 46.0898 73.7887C46.1109 73.8183 46.1363 73.8437 46.1574 73.8733V73.8775C46.187 73.9156 46.2166 73.9495 46.2462 73.9875C46.6944 74.5629 47.1566 75.1256 47.633 75.6756L47.7091 75.7645C47.7344 75.7941 47.764 75.8237 47.7894 75.8576C47.8317 75.9041 47.8697 75.9506 47.912 75.9972C47.9501 76.0437 47.9923 76.0902 48.0346 76.1368C50.5177 78.9358 53.3331 81.4207 56.4184 83.5364C56.8285 83.8199 57.2471 84.0948 57.6657 84.3614C57.7714 84.4291 57.877 84.4968 57.9827 84.5602C58.0123 84.5814 58.0462 84.6026 58.0758 84.6237C58.1349 84.6576 58.1899 84.6914 58.2491 84.7295C58.3041 84.7633 58.359 84.7972 58.4182 84.831C58.6423 84.9706 58.8706 85.106 59.0989 85.2414C59.1539 85.2752 59.2131 85.3091 59.2722 85.343C59.3272 85.3726 59.3864 85.4064 59.4456 85.4403C59.4456 85.4445 59.4456 85.4445 59.4498 85.4445H59.454V85.4487H59.4583C59.7415 85.6137 60.029 85.7745 60.3207 85.931C60.4772 86.0156 60.6378 86.1045 60.7985 86.1849C61.1621 86.3837 61.5299 86.5741 61.8977 86.756C62.0288 86.8195 62.1641 86.8829 62.2952 86.9506C62.4051 87.0014 62.515 87.0564 62.6249 87.1072C62.6587 87.1241 62.6883 87.1368 62.7179 87.1537C62.811 87.196 62.8997 87.2383 62.9927 87.2806C63.0942 87.3272 63.1957 87.3737 63.2929 87.4202C63.4071 87.471 63.517 87.5218 63.6312 87.5683C64.0539 87.7587 64.4809 87.9406 64.908 88.1141C66.4383 88.7418 68.0021 89.2842 69.5924 89.7387C69.6643 89.7599 69.7404 89.781 69.8165 89.8022C70.4972 89.9926 71.1821 90.166 71.8713 90.3226C73.0762 90.6018 74.2924 90.8303 75.5199 91.0079H75.5283C75.6763 91.0291 75.8201 91.0502 75.968 91.0714C75.9807 91.0714 75.9934 91.0756 76.0103 91.0756C76.0103 91.0799 76.0103 91.0799 76.0145 91.0756C76.1752 91.0968 76.3401 91.1179 76.5007 91.1391C76.5117 91.1423 76.5231 91.1438 76.5346 91.1433C76.6868 91.1645 76.8432 91.1814 76.9954 91.1983C77.1603 91.2152 77.3252 91.2322 77.4901 91.2491C77.6169 91.2618 77.7437 91.2745 77.8706 91.2872C77.8814 91.2909 77.8929 91.2924 77.9044 91.2914C78.0185 91.2999 78.1327 91.3126 78.2468 91.321H78.2638C78.361 91.3295 78.4625 91.3379 78.5597 91.3464C79.7393 91.4395 80.9231 91.4818 82.1238 91.4818C83.4217 91.4818 84.7112 91.431 85.9838 91.321H85.9965C86.2713 91.2956 86.5419 91.2702 86.8125 91.2449C86.8843 91.2364 86.952 91.2322 87.0239 91.2237C87.0365 91.2237 87.0535 91.2195 87.0661 91.2195C87.0873 91.2152 87.1084 91.2152 87.1338 91.211C87.1452 91.2115 87.1566 91.21 87.1676 91.2068C87.2606 91.1983 87.3536 91.1856 87.4509 91.1772L87.768 91.1391C87.8737 91.1264 87.9794 91.1095 88.085 91.0968C88.1104 91.0926 88.1316 91.0926 88.1569 91.0883C88.233 91.0799 88.3091 91.0672 88.3852 91.0545C88.4233 91.0503 88.4613 91.046 88.4951 91.0418C88.5713 91.0291 88.6431 91.0207 88.7192 91.008H88.7235C91.0434 90.6738 93.3335 90.1574 95.5726 89.4637C95.691 89.4256 95.8093 89.3918 95.9277 89.3537C95.9319 89.3537 95.9362 89.3495 95.9404 89.3495C95.9742 89.3368 96.0123 89.3283 96.0461 89.3157C96.3632 89.2141 96.676 89.1084 96.9889 88.9984C97.4201 88.8545 97.8472 88.698 98.2742 88.5372C98.3883 88.4907 98.5025 88.4483 98.6166 88.4018C98.8492 88.313 99.0775 88.2199 99.3058 88.1268C99.6947 87.9703 100.084 87.8053 100.468 87.636C100.828 87.4753 101.183 87.3145 101.538 87.1453C101.547 87.1411 101.559 87.1411 101.568 87.1326C101.648 87.0945 101.728 87.0564 101.804 87.0183H101.809C103.756 86.0833 105.633 85.0102 107.427 83.8072C108.002 83.4222 108.569 83.0245 109.127 82.6141C109.575 82.2799 110.015 81.9414 110.455 81.5945C112.624 79.8679 114.632 77.9477 116.454 75.8576V75.8533C116.585 75.7095 116.708 75.5614 116.834 75.4133C116.856 75.3922 116.872 75.371 116.889 75.3541C116.97 75.2568 117.046 75.1637 117.126 75.0706V75.0664C117.232 74.9437 117.338 74.8168 117.439 74.6899C117.464 74.6603 117.486 74.6349 117.507 74.6095V74.6053C117.591 74.5037 117.672 74.4022 117.756 74.2964C117.798 74.2457 117.836 74.1949 117.879 74.1441V74.1399C118.128 73.8268 118.369 73.5137 118.61 73.1922C118.674 73.1076 118.737 73.0187 118.805 72.9299C119.109 72.5195 119.405 72.1006 119.692 71.6818V71.6776C119.802 71.521 119.908 71.3603 120.018 71.2037C120.124 71.043 120.229 70.8822 120.335 70.7214C120.441 70.5522 120.551 70.3872 120.656 70.218L120.669 70.1926C120.775 70.0318 120.88 69.8626 120.982 69.6976C121.062 69.5622 121.143 69.431 121.223 69.2999C121.274 69.211 121.329 69.1222 121.379 69.0291C121.399 69.0005 121.418 68.9708 121.434 68.9403C121.553 68.733 121.671 68.5257 121.789 68.3184C121.929 68.0772 122.064 67.8318 122.2 67.5864C122.25 67.4849 122.305 67.3834 122.36 67.2818C122.381 67.248 122.398 67.2141 122.415 67.1803C122.479 67.0618 122.542 66.9392 122.601 66.8164C122.665 66.698 122.728 66.5753 122.791 66.4526C122.851 66.3341 122.914 66.2114 122.973 66.0887C123.02 65.9957 123.062 65.9026 123.109 65.8095C123.112 65.8019 123.116 65.7948 123.121 65.7884C123.142 65.7418 123.164 65.6995 123.18 65.6572C123.186 65.6508 123.19 65.6436 123.193 65.6361C123.24 65.543 123.282 65.4457 123.328 65.3526C123.388 65.2299 123.443 65.103 123.502 64.9803C123.54 64.8957 123.582 64.8068 123.62 64.718C123.675 64.5953 123.734 64.4726 123.785 64.3457C123.844 64.2188 123.899 64.0918 123.954 63.9607C123.97 63.929 123.984 63.8965 123.996 63.8634C124.026 63.8041 124.047 63.7449 124.073 63.6857C124.14 63.5291 124.208 63.3684 124.271 63.2118C124.347 63.0341 124.419 62.8522 124.487 62.6745C124.554 62.5095 124.618 62.3403 124.681 62.1711L124.707 62.1118C125.692 59.5154 126.439 56.8348 126.939 54.103V54.0987C127.221 52.5391 127.423 50.966 127.544 49.3857C127.548 49.2968 127.556 49.2122 127.56 49.1276C127.637 48.0572 127.67 46.9826 127.67 45.9037C127.67 45.4257 127.662 44.9518 127.649 44.478C127.354 34.568 123.823 25.0276 117.595 17.3164C117.46 17.1472 117.325 16.978 117.185 16.8087C117.046 16.6395 116.902 16.4745 116.763 16.3095C112.128 10.8781 106.283 6.61246 99.6982 3.85643C93.1138 1.10041 85.9744 -0.0688733 78.8555 0.442851C71.7366 0.954575 64.8375 3.13298 58.7143 6.80246C52.5911 10.4719 47.4152 15.5298 43.6038 21.5684C43.3967 21.8984 43.1895 22.2326 42.9908 22.5668C42.7879 22.9053 42.5934 23.2438 42.3989 23.5865C38.5687 30.3993 36.5619 38.0867 36.5729 45.9037Z"
        fill="#F2F2F2"
      />
      <path
        d="M80.3133 16.091C87.8032 18.0142 95.4787 19.1227 103.206 19.3974C104.375 19.4611 105.546 19.4532 106.714 19.3736C109.28 19.1576 111.478 18.3506 113.925 17.9096C115.102 17.6982 116.413 17.5436 117.598 17.3151C117.324 16.9753 117.045 16.6396 116.762 16.3081C114.207 16.524 111.613 17.231 109.028 17.5595C104.633 18.1187 99.7091 17.4947 96.3776 15.9557C94.7613 15.2089 93.5233 14.2769 91.9126 13.5267C91.6936 13.4142 91.4563 13.3414 91.2118 13.3119C90.7474 13.2979 90.2887 13.417 89.8897 13.6551C87.1875 15.0202 83.2643 16.1296 79.6227 15.5192C79.3513 15.7318 79.8669 15.9739 80.3133 16.091Z"
        fill="white"
      />
      <path
        d="M64.199 20.8951C64.1657 20.889 64.1324 20.8843 64.0991 20.8785C64.239 20.7917 64.3707 20.6924 64.4927 20.5818L64.199 20.8951Z"
        fill="white"
      />
      <path
        d="M42.3998 23.5882C43.9207 23.7099 45.4495 23.8004 46.9863 23.8599C48.1549 23.923 49.3262 23.9151 50.4939 23.836C53.0597 23.6189 55.2576 22.813 57.7054 22.372C59.9487 21.9685 62.6747 21.7742 64.0991 20.8785C60.4178 20.2409 56.6036 21.5389 52.8076 22.022C49.737 22.3998 46.6244 22.246 43.606 21.5673C43.1858 22.2288 42.7837 22.9025 42.3998 23.5882Z"
        fill="white"
      />
      <path
        d="M60.7985 86.1848C61.1621 86.3836 61.5299 86.574 61.8977 86.7559C62.0288 86.8194 62.1641 86.8829 62.2951 86.9506C62.405 87.0013 62.515 87.0563 62.6249 87.1071C62.6587 87.124 62.6883 87.1367 62.7179 87.1536C62.8109 87.1959 62.8997 87.2383 62.9927 87.2806C63.0942 87.3271 63.1957 87.3736 63.2929 87.4202C63.4071 87.471 63.517 87.5217 63.6311 87.5682C64.0539 87.7586 64.4809 87.9406 64.9079 88.114C66.4382 88.7417 68.0021 89.2841 69.5924 89.7386C69.6643 89.7598 69.7404 89.781 69.8165 89.8021C70.4972 89.9925 71.1821 90.1659 71.8712 90.3225C73.0762 90.6017 74.2924 90.8302 75.5199 91.0079H75.5283C75.6763 91.029 75.82 91.0502 75.968 91.0713C75.9807 91.0713 75.9934 91.0756 76.0103 91.0756C76.0103 91.0798 76.0103 91.0798 76.0145 91.0756C76.1752 91.0967 76.34 91.1179 76.5007 91.139C76.5117 91.1423 76.5231 91.1437 76.5345 91.1433C76.6867 91.1644 76.8432 91.1813 76.9954 91.1983C77.1602 91.2152 77.3251 91.2321 77.49 91.249C77.6169 91.2617 77.7437 91.2744 77.8705 91.2871C77.8814 91.2909 77.8929 91.2923 77.9044 91.2913C78.0185 91.2998 78.1327 91.3125 78.2468 91.321H78.2637C78.361 91.3294 78.4624 91.3379 78.5597 91.3464C79.7393 91.4394 80.923 91.4817 82.1238 91.4817C83.4217 91.4817 84.7112 91.431 85.9838 91.321H85.9965C86.2713 91.2956 86.5419 91.2702 86.8124 91.2448C86.8843 91.2363 86.952 91.2321 87.0238 91.2236C87.0365 91.2236 87.0534 91.2194 87.0661 91.2194C87.0872 91.2152 87.1084 91.2152 87.1338 91.2109C87.1452 91.2114 87.1566 91.21 87.1676 91.2067C87.2606 91.1983 87.3536 91.1856 87.4508 91.1771L87.7679 91.139C87.8736 91.1263 87.9793 91.1094 88.085 91.0967C88.1104 91.0925 88.1315 91.0925 88.1569 91.0883C88.233 91.0798 88.3091 91.0671 88.3852 91.0544C88.4232 91.0502 88.4613 91.046 88.4951 91.0417C88.5712 91.029 88.6431 91.0206 88.7192 91.0079H88.7234C91.0434 90.6737 93.3335 90.1573 95.5725 89.4637C95.6909 89.4256 95.8093 89.3917 95.9277 89.3537C95.3818 86.806 94.2579 84.4181 92.643 82.3743C91.028 80.3306 88.9651 78.6857 86.6137 77.5667C87.1953 77.0123 87.5974 76.296 87.7679 75.5106C87.8777 75.0042 87.8864 74.4812 87.7935 73.9715C87.7006 73.4618 87.508 72.9755 87.2268 72.5406C86.6781 71.6976 85.8174 71.1071 84.8338 70.899L78.5681 69.5875C77.5595 69.3907 76.514 69.6003 75.659 70.1707C74.8039 70.7412 74.2085 71.6263 74.0021 72.6337C73.9381 72.9425 73.9098 73.2576 73.9175 73.5729C67.8843 77.5921 63.5423 81.7594 60.7985 86.1848Z"
        fill="#6D11D2"
      />
      <path
        d="M85.5858 65.3552C94.0088 65.3552 100.837 58.5223 100.837 50.0935C100.837 41.6647 94.0088 34.8318 85.5858 34.8318C77.1628 34.8318 70.3346 41.6647 70.3346 50.0935C70.3346 58.5223 77.1628 65.3552 85.5858 65.3552Z"
        fill="#9E616A"
      />
      <path
        d="M83.4184 64.5394C79.6003 65.5713 75.3909 64.1994 72.4112 61.597C69.4316 58.9946 67.5677 55.3071 66.4937 51.4982C65.7071 48.7086 65.3342 45.612 66.5754 42.9931C67.8166 40.3742 71.1572 38.6074 73.7344 39.9302C72.1445 38.0766 72.9388 35.0108 74.7957 33.4251C76.6525 31.8394 79.2023 31.3848 81.6307 31.1363C85.155 30.7756 88.7954 30.7352 92.1491 31.8778C95.5027 33.0203 98.5499 35.5183 99.5994 38.9043C100.671 39.4038 101.879 39.5333 103.032 39.2727C104.186 39.0121 105.22 38.3761 105.974 37.4641C106.168 38.5115 106.012 39.5937 105.53 40.5433C105.047 41.493 104.266 42.2571 103.306 42.7175L108.444 41.0505C109.328 42.9223 107.78 45.2389 105.799 45.8384C103.819 46.4378 101.682 45.7826 99.8027 44.9166C97.9237 44.0506 96.1027 42.9558 94.0617 42.618C92.0206 42.2803 89.6231 42.9366 88.679 44.7786C88.1845 45.7434 88.1588 46.8734 87.8717 47.9189C87.5846 48.9644 86.8573 50.0484 85.7771 50.134C85.0947 50.1881 84.4265 49.8243 83.746 49.8987C83.3284 49.981 82.9438 50.1834 82.6393 50.481C82.3349 50.7787 82.1238 51.1588 82.0319 51.5747C81.8624 52.4093 81.8603 53.2692 82.0258 54.1047L83.331 64.0884L83.4184 64.5394Z"
        fill="#2F2E41"
      />
    </svg>
  );
};

export const TodoFilterIcon = ({ size = 22, stroke = "#2D394A" }) => {
  return (
    <svg
      width={size}
      height={size}
      viewBox="0 0 22 22"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M2.75 6.41699H5.5"
        stroke={stroke}
        strokeWidth="2"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M2.75 15.583H8.25"
        stroke={stroke}
        strokeWidth="2"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M16.5 15.583H19.25"
        stroke={stroke}
        strokeWidth="2"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M13.75 6.41699H19.25"
        stroke={stroke}
        strokeWidth="2"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M5.5 6.41699C5.5 5.56277 5.5 5.13566 5.63955 4.79874C5.82563 4.34952 6.18253 3.99262 6.63174 3.80655C6.96867 3.66699 7.39578 3.66699 8.25 3.66699C9.10422 3.66699 9.53132 3.66699 9.86828 3.80655C10.3175 3.99262 10.6744 4.34952 10.8605 4.79874C11 5.13566 11 5.56277 11 6.41699C11 7.27122 11 7.69833 10.8605 8.03525C10.6744 8.48446 10.3175 8.84136 9.86828 9.02744C9.53132 9.16699 9.10422 9.16699 8.25 9.16699C7.39578 9.16699 6.96867 9.16699 6.63174 9.02744C6.18253 8.84136 5.82563 8.48446 5.63955 8.03525C5.5 7.69833 5.5 7.27122 5.5 6.41699Z"
        stroke={stroke}
        strokeWidth="2"
      />
      <path
        d="M11 15.583C11 14.7288 11 14.3017 11.1395 13.9647C11.3256 13.5156 11.6826 13.1586 12.1317 12.9725C12.4687 12.833 12.8958 12.833 13.75 12.833C14.6042 12.833 15.0313 12.833 15.3683 12.9725C15.8175 13.1586 16.1744 13.5156 16.3605 13.9647C16.5 14.3017 16.5 14.7288 16.5 15.583C16.5 16.4372 16.5 16.8643 16.3605 17.2013C16.1744 17.6505 15.8175 18.0074 15.3683 18.1935C15.0313 18.333 14.6042 18.333 13.75 18.333C12.8958 18.333 12.4687 18.333 12.1317 18.1935C11.6826 18.0074 11.3256 17.6505 11.1395 17.2013C11 16.8643 11 16.4372 11 15.583Z"
        stroke={stroke}
        strokeWidth="2"
      />
    </svg>
  );
};

export const TodoUploadImageIcon = ({ width = 100, height = 122 }) => {
  return (
    <svg
      width={width}
      height={height}
      viewBox="0 0 100 122"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <rect
        x="0.5"
        y="0.5"
        width="99"
        height="121"
        rx="13.5"
        fill="#F6EFFE"
        stroke="#6D11D2"
        strokeDasharray="6 6"
      />
      <circle cx="50" cy="49" r="19" fill="#6D11D2" />
      <path
        d="M50.3771 41.4014H50.5844C53.0629 41.4014 54.3022 41.4014 55.1628 42.0077C55.4094 42.1815 55.6283 42.3875 55.8129 42.6196C56.4571 43.4296 56.4571 44.5959 56.4571 46.9286V48.8632C56.4571 51.1152 56.4571 52.2412 56.1007 53.1405C55.5278 54.5863 54.3161 55.7267 52.78 56.2659C51.8244 56.6014 50.6281 56.6014 48.2353 56.6014C46.868 56.6014 46.1844 56.6014 45.6383 56.4097C44.7606 56.1016 44.0682 55.4499 43.7408 54.6237C43.5371 54.1098 43.5371 53.4664 43.5371 52.1795V49.0014"
        stroke="white"
        strokeWidth="1.14"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M56.4574 49.001C56.4574 50.4001 55.3232 51.5343 53.9241 51.5343C53.4181 51.5343 52.8216 51.4456 52.3296 51.5775C51.8925 51.6946 51.551 52.036 51.4339 52.4731C51.3021 52.9651 51.3908 53.5616 51.3908 54.0676C51.3908 55.4668 50.2565 56.601 48.8574 56.601"
        stroke="white"
        strokeWidth="1.14"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M44.2969 43.3014C44.6704 42.917 45.6647 41.4014 46.1969 41.4014M48.0969 43.3014C47.7233 42.917 46.729 41.4014 46.1969 41.4014M46.1969 41.4014V47.4814"
        stroke="white"
        strokeWidth="1.14"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M31.0298 78.3636H33.1378V85.9659C33.1378 86.7992 32.9408 87.5322 32.5469 88.1648C32.1567 88.7973 31.6075 89.2917 30.8991 89.6477C30.1908 90 29.3632 90.1761 28.4162 90.1761C27.4654 90.1761 26.6359 90 25.9276 89.6477C25.2192 89.2917 24.67 88.7973 24.2798 88.1648C23.8897 87.5322 23.6946 86.7992 23.6946 85.9659V78.3636H25.8026V85.7898C25.8026 86.2746 25.9086 86.7064 26.1207 87.0852C26.3366 87.464 26.6397 87.7614 27.0298 87.9773C27.42 88.1894 27.8821 88.2955 28.4162 88.2955C28.9503 88.2955 29.4124 88.1894 29.8026 87.9773C30.1965 87.7614 30.4995 87.464 30.7116 87.0852C30.9238 86.7064 31.0298 86.2746 31.0298 85.7898V78.3636ZM35.3381 93.2727V81.2727H37.3608V82.7159H37.4801C37.5862 82.5038 37.7358 82.2784 37.929 82.0398C38.1222 81.7973 38.3835 81.5909 38.7131 81.4205C39.0426 81.2462 39.4631 81.1591 39.9744 81.1591C40.6487 81.1591 41.2566 81.3314 41.7983 81.6761C42.3438 82.017 42.7756 82.5227 43.0938 83.1932C43.4157 83.8598 43.5767 84.678 43.5767 85.6477C43.5767 86.6061 43.4195 87.4205 43.1051 88.0909C42.7907 88.7614 42.3627 89.2727 41.821 89.625C41.2794 89.9773 40.6657 90.1534 39.9801 90.1534C39.4801 90.1534 39.0653 90.0701 38.7358 89.9034C38.4063 89.7367 38.1411 89.536 37.9403 89.3011C37.7434 89.0625 37.59 88.8371 37.4801 88.625H37.3949V93.2727H35.3381ZM37.3551 85.6364C37.3551 86.2008 37.4347 86.6951 37.5938 87.1193C37.7566 87.5436 37.9896 87.875 38.2926 88.1136C38.5994 88.3485 38.9706 88.4659 39.4062 88.4659C39.8608 88.4659 40.2415 88.3447 40.5483 88.1023C40.8551 87.8561 41.0862 87.5208 41.2415 87.0966C41.4006 86.6686 41.4801 86.1818 41.4801 85.6364C41.4801 85.0947 41.4025 84.6136 41.2472 84.1932C41.0919 83.7727 40.8608 83.4432 40.554 83.2045C40.2472 82.9659 39.8646 82.8466 39.4062 82.8466C38.9669 82.8466 38.5938 82.9621 38.2869 83.1932C37.9801 83.4242 37.7472 83.7481 37.5881 84.1648C37.4328 84.5814 37.3551 85.072 37.3551 85.6364ZM47.3949 78.3636V90H45.3381V78.3636H47.3949ZM53.3168 90.1705C52.4645 90.1705 51.7259 89.983 51.1009 89.608C50.4759 89.233 49.991 88.7083 49.6463 88.0341C49.3054 87.3598 49.1349 86.572 49.1349 85.6705C49.1349 84.7689 49.3054 83.9792 49.6463 83.3011C49.991 82.6231 50.4759 82.0966 51.1009 81.7216C51.7259 81.3466 52.4645 81.1591 53.3168 81.1591C54.169 81.1591 54.9077 81.3466 55.5327 81.7216C56.1577 82.0966 56.6406 82.6231 56.9815 83.3011C57.3262 83.9792 57.4986 84.7689 57.4986 85.6705C57.4986 86.572 57.3262 87.3598 56.9815 88.0341C56.6406 88.7083 56.1577 89.233 55.5327 89.608C54.9077 89.983 54.169 90.1705 53.3168 90.1705ZM53.3281 88.5227C53.7902 88.5227 54.1766 88.3958 54.4872 88.142C54.7978 87.8845 55.0289 87.5398 55.1804 87.108C55.3357 86.6761 55.4134 86.1951 55.4134 85.6648C55.4134 85.1307 55.3357 84.6477 55.1804 84.2159C55.0289 83.7803 54.7978 83.4337 54.4872 83.1761C54.1766 82.9186 53.7902 82.7898 53.3281 82.7898C52.8546 82.7898 52.4607 82.9186 52.1463 83.1761C51.8357 83.4337 51.6027 83.7803 51.4474 84.2159C51.2959 84.6477 51.2202 85.1307 51.2202 85.6648C51.2202 86.1951 51.2959 86.6761 51.4474 87.108C51.6027 87.5398 51.8357 87.8845 52.1463 88.142C52.4607 88.3958 52.8546 88.5227 53.3281 88.5227ZM61.7557 90.1761C61.2027 90.1761 60.7045 90.0777 60.2614 89.8807C59.822 89.6799 59.4735 89.3845 59.2159 88.9943C58.9621 88.6042 58.8352 88.1231 58.8352 87.5511C58.8352 87.0587 58.9261 86.6515 59.108 86.3295C59.2898 86.0076 59.5379 85.75 59.8523 85.5568C60.1667 85.3636 60.5208 85.2178 60.9148 85.1193C61.3125 85.017 61.7235 84.9432 62.1477 84.8977C62.6591 84.8447 63.0739 84.7973 63.392 84.7557C63.7102 84.7102 63.9413 84.642 64.0852 84.5511C64.233 84.4564 64.3068 84.3106 64.3068 84.1136V84.0795C64.3068 83.6515 64.1799 83.3201 63.9261 83.0852C63.6723 82.8504 63.3068 82.733 62.8295 82.733C62.3258 82.733 61.9261 82.8428 61.6307 83.0625C61.339 83.2822 61.142 83.5417 61.0398 83.8409L59.1193 83.5682C59.2708 83.0379 59.5208 82.5947 59.8693 82.2386C60.2178 81.8788 60.6439 81.6098 61.1477 81.4318C61.6515 81.25 62.2083 81.1591 62.8182 81.1591C63.2386 81.1591 63.6572 81.2083 64.0739 81.3068C64.4905 81.4053 64.8712 81.5682 65.2159 81.7955C65.5606 82.0189 65.8371 82.3239 66.0455 82.7102C66.2576 83.0966 66.3636 83.5795 66.3636 84.1591V90H64.3864V88.8011H64.3182C64.1932 89.0436 64.017 89.2708 63.7898 89.483C63.5663 89.6913 63.2841 89.8598 62.9432 89.9886C62.6061 90.1136 62.2102 90.1761 61.7557 90.1761ZM62.2898 88.6648C62.7027 88.6648 63.0606 88.5833 63.3636 88.4205C63.6667 88.2538 63.8996 88.0341 64.0625 87.7614C64.2292 87.4886 64.3125 87.1913 64.3125 86.8693V85.8409C64.2481 85.8939 64.1383 85.9432 63.983 85.9886C63.8314 86.0341 63.661 86.0739 63.4716 86.108C63.2822 86.142 63.0947 86.1723 62.9091 86.1989C62.7235 86.2254 62.5625 86.2481 62.4261 86.267C62.1193 86.3087 61.8447 86.3769 61.6023 86.4716C61.3598 86.5663 61.1686 86.6989 61.0284 86.8693C60.8883 87.036 60.8182 87.2519 60.8182 87.517C60.8182 87.8958 60.9564 88.1818 61.233 88.375C61.5095 88.5682 61.8617 88.6648 62.2898 88.6648ZM71.6705 90.1534C70.9848 90.1534 70.3712 89.9773 69.8295 89.625C69.2879 89.2727 68.8598 88.7614 68.5455 88.0909C68.2311 87.4205 68.0739 86.6061 68.0739 85.6477C68.0739 84.678 68.233 83.8598 68.5511 83.1932C68.8731 82.5227 69.3068 82.017 69.8523 81.6761C70.3977 81.3314 71.0057 81.1591 71.6761 81.1591C72.1875 81.1591 72.608 81.2462 72.9375 81.4205C73.267 81.5909 73.5284 81.7973 73.7216 82.0398C73.9148 82.2784 74.0644 82.5038 74.1705 82.7159H74.2557V78.3636H76.3182V90H74.2955V88.625H74.1705C74.0644 88.8371 73.911 89.0625 73.7102 89.3011C73.5095 89.536 73.2443 89.7367 72.9148 89.9034C72.5852 90.0701 72.1705 90.1534 71.6705 90.1534ZM72.2443 88.4659C72.6799 88.4659 73.0511 88.3485 73.358 88.1136C73.6648 87.875 73.8977 87.5436 74.0568 87.1193C74.2159 86.6951 74.2955 86.2008 74.2955 85.6364C74.2955 85.072 74.2159 84.5814 74.0568 84.1648C73.9015 83.7481 73.6705 83.4242 73.3636 83.1932C73.0606 82.9621 72.6875 82.8466 72.2443 82.8466C71.786 82.8466 71.4034 82.9659 71.0966 83.2045C70.7898 83.4432 70.5587 83.7727 70.4034 84.1932C70.2481 84.6136 70.1705 85.0947 70.1705 85.6364C70.1705 86.1818 70.2481 86.6686 70.4034 87.0966C70.5625 87.5208 70.7955 87.8561 71.1023 88.1023C71.4129 88.3447 71.7936 88.4659 72.2443 88.4659Z"
        fill="#2D394A"
      />
    </svg>
  );
};

export const DocIcon = ({
  height = 60,
  width = 45,
  fill = "#0263D1",
  textColor = "#fff",
}) => {
  return (
    <svg
      width={width}
      height={height}
      viewBox="0 0 45 61"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M7.81607 0H27.9193L44.9365 17.7543V52.2667C44.9365 56.5838 41.4375 60.0828 37.1205 60.0828H7.81607C3.49897 60.0828 0 56.5837 0 52.2667V7.81607C0 3.49905 3.49897 0 7.81607 0Z"
        fill={fill}
      />
      <path
        opacity="0.302"
        fillRule="evenodd"
        clipRule="evenodd"
        d="M27.959 0V17.6085H45.0006L27.959 0Z"
        fill="white"
      />
      <path
        d="M9.76367 42.195V33.6009H12.808C13.4168 33.6009 13.9838 33.6919 14.5087 33.8599C15.0335 34.0348 15.5094 34.2868 15.9364 34.6227C16.3632 34.9586 16.6992 35.4066 16.9441 35.9664C17.1891 36.5263 17.3151 37.1701 17.3151 37.898C17.3151 38.6258 17.1891 39.2697 16.9441 39.8295C16.6992 40.3893 16.3632 40.8373 15.9364 41.1732C15.5095 41.5091 15.0335 41.761 14.5087 41.936C13.9838 42.104 13.4169 42.195 12.808 42.195H9.76367ZM11.9122 40.3264H12.549C12.892 40.3264 13.2139 40.2845 13.5008 40.2075C13.7947 40.1235 14.0606 39.9905 14.3126 39.8155C14.5645 39.6405 14.7605 39.3887 14.9004 39.0597C15.0474 38.7378 15.1174 38.3458 15.1174 37.898C15.1174 37.4501 15.0474 37.0582 14.9004 36.7292C14.7605 36.4073 14.5645 36.1553 14.3126 35.9804C14.0606 35.7984 13.7947 35.6725 13.5008 35.5885C13.2139 35.5115 12.892 35.4695 12.549 35.4695H11.9122V40.3264ZM22.5289 42.293C21.2342 42.293 20.1634 41.8731 19.3166 41.0403C18.4698 40.2075 18.0499 39.1577 18.0499 37.898C18.0499 36.6383 18.4698 35.5885 19.3166 34.7558C20.1634 33.9229 21.2342 33.5031 22.5289 33.5031C23.8026 33.5031 24.8593 33.923 25.7062 34.7558C26.5459 35.5886 26.9659 36.6384 26.9659 37.898C26.9659 39.1577 26.5459 40.2075 25.7062 41.0403C24.8593 41.8731 23.8026 42.293 22.5289 42.293ZM20.8772 39.7106C21.3041 40.1865 21.85 40.4244 22.5148 40.4244C23.1797 40.4244 23.7185 40.1865 24.1455 39.7106C24.5724 39.2277 24.7823 38.6258 24.7823 37.898C24.7823 37.1701 24.5724 36.5683 24.1455 36.0854C23.7186 35.6095 23.1797 35.3715 22.5148 35.3715C21.85 35.3715 21.3041 35.6095 20.8772 36.0854C20.4503 36.5683 20.2333 37.1701 20.2333 37.898C20.2333 38.6258 20.4503 39.2277 20.8772 39.7106ZM32.0747 42.293C30.822 42.293 29.7792 41.9011 28.9534 41.1313C28.1206 40.3545 27.7077 39.2767 27.7077 37.898C27.7077 36.5263 28.1277 35.4485 28.9675 34.6717C29.8143 33.8949 30.8431 33.5029 32.0748 33.5029C33.1875 33.5029 34.0973 33.7759 34.8182 34.3288C35.532 34.8746 35.9449 35.6025 36.0499 36.5122L33.8804 36.9532C33.7894 36.4773 33.5724 36.0924 33.2365 35.8054C32.9006 35.5184 32.5087 35.3715 32.0607 35.3715C31.4449 35.3715 30.934 35.5885 30.5211 36.0293C30.1081 36.4772 29.8982 37.0931 29.8982 37.8979C29.8982 38.7027 30.1081 39.3186 30.514 39.7595C30.9269 40.2074 31.4379 40.4244 32.0607 40.4244C32.5085 40.4244 32.8935 40.2984 33.2084 40.0465C33.5233 39.7945 33.7193 39.4586 33.8033 39.0387L36.0218 39.5426C35.8189 40.4104 35.3709 41.0823 34.6711 41.5651C33.9783 42.0481 33.1105 42.293 32.0747 42.293Z"
        fill="white"
      />
    </svg>
  );
};

export const PdfIcon = ({
  height = 60,
  width = 45,
  fill = "#E5252A",
  textColor = "#fff",
}) => {
  return (
    <svg
      width={width}
      height={height}
      viewBox="0 0 45 60"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M7.78738 0H27.8168L44.7716 17.6892V52.0748C44.7716 56.3761 41.2854 59.8622 36.9842 59.8622H7.78738C3.48613 59.8622 0 56.376 0 52.0748V7.78738C0 3.4862 3.48613 0 7.78738 0Z"
        fill={fill}
      />
      <path
        d="M9.91504 43.7725V34.0479H14.0415C15.0631 34.0479 15.8725 34.3272 16.4828 34.8993C17.0931 35.458 17.3983 36.2163 17.3983 37.1608C17.3983 38.1053 17.0931 38.8636 16.4828 39.4224C15.8725 39.9944 15.0631 40.2738 14.0415 40.2738H12.3962V43.7725H9.91504ZM12.3962 38.1586H13.7628C14.1343 38.1586 14.4262 38.0788 14.6253 37.8926C14.8243 37.7196 14.9305 37.4801 14.9305 37.1609C14.9305 36.8416 14.8243 36.6022 14.6253 36.4292C14.4263 36.2429 14.1344 36.1632 13.7628 36.1632H12.3962V38.1586ZM18.4199 43.7725V34.0479H21.8564C22.5331 34.0479 23.1699 34.1409 23.767 34.3405C24.364 34.5401 24.9081 34.8195 25.3857 35.2053C25.8634 35.5777 26.2481 36.0832 26.5268 36.7218C26.7921 37.3604 26.9381 38.0921 26.9381 38.9168C26.9381 39.7284 26.7922 40.46 26.5268 41.0985C26.2481 41.7371 25.8634 42.2426 25.3857 42.6151C24.908 43.0009 24.364 43.2803 23.767 43.4798C23.1699 43.6794 22.5331 43.7725 21.8564 43.7725H18.4199ZM20.848 41.6573H21.5645C21.9492 41.6573 22.3075 41.6175 22.6392 41.5243C22.9576 41.4312 23.2628 41.2848 23.5547 41.0853C23.8333 40.8858 24.0589 40.6063 24.2181 40.2339C24.3773 39.8614 24.4569 39.4224 24.4569 38.9168C24.4569 38.398 24.3773 37.959 24.2181 37.5866C24.0589 37.2141 23.8333 36.9347 23.5547 36.7351C23.2628 36.5356 22.9577 36.3892 22.6392 36.2961C22.3075 36.2031 21.9492 36.1631 21.5645 36.1631H20.848V41.6573ZM28.1853 43.7725V34.0479H35.0848V36.1631H30.6665V37.7195H34.1958V39.8215H30.6665V43.7725H28.1853Z"
        fill="white"
      />
      <path
        opacity="0.302"
        fillRule="evenodd"
        clipRule="evenodd"
        d="M28.0205 0V17.5439H44.9995L28.0205 0Z"
        fill="white"
      />
    </svg>
  );
};

export const DownloadIcon = ({ size = 15, stroke = "#6D11D2" }) => {
  return (
    <svg
      width={size}
      height={size}
      viewBox="0 0 18 17"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M9 11L9 1M9 11C8.29977 11 6.99153 9.0057 6.5 8.5M9 11C9.70023 11 11.0085 9.0057 11.5 8.5"
        stroke={stroke}
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M17 13C17 15.482 16.482 16 14 16H4C1.518 16 1 15.482 1 13"
        stroke={stroke}
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  );
};

export const AddFileIcon = ({ size = 24, stroke = "#fff" }) => {
  return (
    <svg
      width={size}
      height={size}
      viewBox="0 0 24 24"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M18 13.5V21.5M22 17.5L14 17.5"
        stroke={stroke}
        strokeWidth="1.8"
        strokeLinecap="round"
      />
      <path
        d="M7 6.5H16.75C18.8567 6.5 19.91 6.5 20.6667 7.00559C20.9943 7.22447 21.2755 7.50572 21.4944 7.83329C21.9587 8.52819 21.9963 9.68416 21.9994 11.5M12 6.5L11.3666 5.23313C10.8418 4.18358 10.3622 3.12712 9.19926 2.69101C8.6899 2.5 8.10802 2.5 6.94427 2.5C5.1278 2.5 4.21956 2.5 3.53806 2.88032C3.05227 3.15142 2.65142 3.55227 2.38032 4.03806C2 4.71956 2 5.6278 2 7.44427V10.5C2 15.214 2 17.5711 3.46447 19.0355C4.8215 20.3926 6.94493 20.4921 11 20.4994"
        stroke={stroke}
        strokeWidth="1.8"
        strokeLinecap="round"
      />
    </svg>
  );
};

export const AddTeamIcon = ({ size = 18, stroke = "#fff" }) => {
  return (
    <svg
      width={size}
      height={size}
      viewBox="0 0 18 18"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <g clipPath="url(#clip0_4068_17082)">
        <path
          d="M9.375 16.5H4.94315C3.78412 16.5 2.86223 15.936 2.0345 15.1474C0.340023 13.5331 3.1221 12.243 4.18318 11.6112C5.75878 10.673 7.60263 10.3289 9.375 10.5789C10.0181 10.6696 10.6445 10.8385 11.25 11.0857"
          stroke={stroke}
          strokeWidth="1.8"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
        <path
          d="M12.375 4.875C12.375 6.73896 10.864 8.25 9 8.25C7.13604 8.25 5.625 6.73896 5.625 4.875C5.625 3.01104 7.13604 1.5 9 1.5C10.864 1.5 12.375 3.01104 12.375 4.875Z"
          stroke={stroke}
          strokeWidth="1.8"
        />
        <path
          d="M13.875 16.5L13.875 11.25M11.25 13.875H16.5"
          stroke={stroke}
          strokeWidth="1.8"
          strokeLinecap="round"
        />
      </g>
      <defs>
        <clipPath id="clip0_4068_17082">
          <rect width="18" height="18" fill="white" />
        </clipPath>
      </defs>
    </svg>
  );
};

export const UploadFileIcon = ({ size = 23, stroke = "#fff" }) => {
  return (
    <svg
      width={size}
      height={size}
      viewBox="0 0 23 22"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M11.959 1.83337H12.209C15.1984 1.83337 16.6931 1.83337 17.7312 2.56472C18.0286 2.77427 18.2926 3.02277 18.5153 3.30269C19.2923 4.27966 19.2923 5.68645 19.2923 8.50004V10.8334C19.2923 13.5496 19.2923 14.9077 18.8625 15.9924C18.1714 17.7362 16.7099 19.1117 14.8572 19.7621C13.7047 20.1667 12.2617 20.1667 9.37565 20.1667C7.72651 20.1667 6.90193 20.1667 6.24336 19.9355C5.18463 19.5639 4.34951 18.7779 3.95462 17.7814C3.70898 17.1616 3.70898 16.3855 3.70898 14.8334V11"
        stroke={stroke}
        strokeWidth="1.65"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M19.2926 11.0001C19.2926 12.6876 17.9246 14.0556 16.2371 14.0556C15.6268 14.0556 14.9073 13.9487 14.3139 14.1077C13.7867 14.2489 13.3749 14.6608 13.2336 15.188C13.0746 15.7814 13.1815 16.5009 13.1815 17.1112C13.1815 18.7987 11.8135 20.1667 10.126 20.1667"
        stroke={stroke}
        strokeWidth="1.65"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M4.62598 4.12504C5.07655 3.66148 6.27577 1.83337 6.91764 1.83337M9.20931 4.12504C8.75874 3.66148 7.55952 1.83337 6.91764 1.83337M6.91764 1.83337L6.91764 9.16671"
        stroke={stroke}
        strokeWidth="1.65"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  );
};

export const DocumentIcon = ({ width = 18, height = 24 }) => {
  return (
    <svg
      width={width}
      height={height}
      viewBox="0 0 18 24"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M3.14427 0H11.1766L17.9764 7.09058V20.8794C17.9764 22.6013 16.5777 24 14.8512 24H3.1443C1.42242 24 0.0236969 22.6013 0.0236969 20.8794V3.12061C0.0236666 1.39872 1.42239 0 3.14427 0Z"
        fill="#0263D1"
      />
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M11.1674 0V7.03521H17.9764L11.1674 0Z"
        fill="white"
        fillOpacity="0.5"
      />
      <path
        d="M12.1645 12.487H5.83555C5.54472 12.487 5.30467 12.2516 5.30467 11.9608C5.30467 11.67 5.54472 11.4345 5.83555 11.4345H12.1645C12.4553 11.4345 12.6907 11.67 12.6907 11.9608C12.6907 12.2516 12.4553 12.487 12.1645 12.487ZM10.0548 18.8159H5.83555C5.54472 18.8159 5.30467 18.5805 5.30467 18.2897C5.30467 17.9989 5.54472 17.7635 5.83555 17.7635H10.0548C10.3457 17.7635 10.5811 17.9989 10.5811 18.2897C10.5811 18.5805 10.3457 18.8159 10.0548 18.8159ZM12.1645 16.7063H5.83555C5.54472 16.7063 5.30467 16.4709 5.30467 16.1801C5.30467 15.8893 5.54472 15.6538 5.83555 15.6538H12.1645C12.4553 15.6538 12.6907 15.8893 12.6907 16.1801C12.6907 16.4709 12.4553 16.7063 12.1645 16.7063ZM12.1645 14.5967H5.83555C5.54472 14.5967 5.30467 14.3612 5.30467 14.0704C5.30467 13.7796 5.54472 13.5442 5.83555 13.5442H12.1645C12.4553 13.5442 12.6907 13.7796 12.6907 14.0704C12.6907 14.3612 12.4553 14.5967 12.1645 14.5967Z"
        fill="white"
        fillOpacity="0.5"
      />
    </svg>
  );
};
export const ImgPreviewIcon = ({ width = 18, height = 24 }) => {
  return (
    <svg
      width={width}
      height={height}
      viewBox="0 0 18 24"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M3.14969 0H11.1767L17.9759 7.09344V20.8744C17.9759 22.5996 16.5755 24 14.8503 24H3.14969C1.42454 24 0.0241151 22.5996 0.0241151 20.8744V3.12558C0.0241151 1.40042 1.42454 0 3.14969 0Z"
        fill="#0AC963"
      />
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M11.1666 0V7.03255H17.9759L11.1666 0Z"
        fill="white"
        fillOpacity="0.5"
      />
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M12.0495 11.9949H5.95053C5.49389 11.9949 5.11838 12.3704 5.11838 12.8271V16.7036C5.11838 17.1603 5.49386 17.5358 5.95053 17.5358H12.0495C12.5061 17.5358 12.8715 17.1603 12.8715 16.7036V12.8271C12.8715 12.3704 12.5061 11.9949 12.0495 11.9949ZM7.6148 13.0402C8.11205 13.0402 8.50781 13.4461 8.50781 13.9332C8.50781 14.4304 8.11205 14.8363 7.6148 14.8363C7.11755 14.8363 6.71163 14.4304 6.71163 13.9332C6.71163 13.4461 7.11755 13.0402 7.6148 13.0402ZM12.3235 16.7036C12.3235 16.8558 12.2017 16.9877 12.0495 16.9877H5.95053C5.79831 16.9877 5.67653 16.8558 5.67653 16.7036V16.5412L6.78265 15.4351L7.69598 16.3485C7.8076 16.4601 7.98012 16.4601 8.09174 16.3485L10.3852 14.055L12.3235 15.9933V16.7036Z"
        fill="white"
        fillOpacity="0.5"
      />
    </svg>
  );
};
export const PPTIcon = ({ width = 18, height = 24 }) => {
  return (
    <svg
      width={width}
      height={height}
      viewBox="0 0 18 24"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M3.14572 0H11.1728L17.9751 7.09007V20.8792C17.9751 22.5982 16.5733 24 14.8543 24H3.14572C1.41932 24 0.0249023 22.5982 0.0249023 20.8792V3.12082C0.0249023 1.39442 1.41932 0 3.14572 0Z"
        fill="#E03303"
      />
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M11.1654 0V7.03106H17.9751L11.1654 0Z"
        fill="white"
        fillOpacity="0.5"
      />
      <path
        d="M8.70118 11.9004C6.901 11.989 5.46971 13.4866 5.49185 15.309C5.52135 17.1239 6.99691 18.6069 8.81186 18.629C10.6415 18.6585 12.1392 17.2272 12.2204 15.427C12.2278 15.3385 12.154 15.2647 12.0655 15.2647H9.01843C8.92988 15.2647 8.8561 15.1909 8.8561 15.1098V12.0553C8.85613 11.9668 8.78973 11.9004 8.70118 11.9004ZM9.51274 11.4061V14.4531C9.51274 14.5417 9.57915 14.608 9.66766 14.608H12.7221C12.8106 14.608 12.877 14.5343 12.877 14.4457C12.7959 12.7193 11.4088 11.3323 9.67503 11.2511C9.58652 11.2438 9.51274 11.3176 9.51274 11.4061Z"
        fill="white"
        fillOpacity="0.5"
      />
    </svg>
  );
};
export const ExcelIcon = ({ width = 18, height = 24 }) => {
  return (
    <svg
      width={width}
      height={height}
      viewBox="0 0 18 24"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M3.1497 0H11.1767L17.9759 7.09344V20.8744C17.9759 22.5996 16.5755 24 14.8503 24H3.1497C1.42454 24 0.0241165 22.5996 0.0241165 20.8744V3.12558C0.0241165 1.40042 1.42454 0 3.1497 0Z"
        fill="#00733B"
      />
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M11.1666 0V7.03255H17.9759L11.1666 0Z"
        fill="white"
        fillOpacity="0.5"
      />
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M5.31121 17.6068H8.49768V18.8144H5.31121V17.6068ZM9.49219 11.4368H12.6888V12.6343H9.49219V11.4368ZM5.31121 11.4368H8.49768V12.6343H5.31121V11.4368ZM9.49219 13.4664H12.6888V14.674H9.49219V13.4664ZM5.31121 13.4664H8.49768V14.674H5.31121V13.4664ZM9.49219 15.5772H12.6888V16.7848H9.49219V15.5772ZM5.31121 15.5772H8.49768V16.7848H5.31121V15.5772ZM9.49219 17.6068H12.6888V18.8144H9.49219V17.6068Z"
        fill="white"
        fillOpacity="0.5"
      />
    </svg>
  );
};
export const TextFileIcon = ({ width = 18, height = 24 }) => {
  return (
    <svg
      width={width}
      height={height}
      viewBox="0 0 18 24"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M3.14572 0H11.1728L17.9751 7.09007V20.8792C17.9751 22.5982 16.5733 24 14.8543 24H3.14572C1.41932 24 0.0249023 22.5982 0.0249023 20.8792V3.12082C0.0249023 1.39442 1.41932 0 3.14572 0Z"
        fill="#251D36"
      />
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M11.1654 0V7.03106H17.9751L11.1654 0Z"
        fill="white"
        fillOpacity="0.5"
      />
      <path
        d="M12.1614 15.6483H5.83861C5.76877 15.648 5.69956 15.6616 5.63498 15.6882C5.57039 15.7148 5.51172 15.7539 5.46233 15.8033C5.41294 15.8527 5.37383 15.9113 5.34723 15.9759C5.32064 16.0405 5.3071 16.1097 5.30739 16.1795C5.30739 16.4673 5.5435 16.7034 5.83861 16.7034H12.1614C12.4565 16.7034 12.6926 16.4673 12.6926 16.1795C12.6929 16.1097 12.6794 16.0405 12.6528 15.9759C12.6262 15.9113 12.5871 15.8527 12.5377 15.8033C12.4883 15.7539 12.4296 15.7148 12.365 15.6882C12.3004 15.6616 12.2312 15.648 12.1614 15.6483ZM12.1614 17.7584H5.83861C5.76877 17.7581 5.69956 17.7716 5.63498 17.7982C5.57039 17.8248 5.51172 17.8639 5.46233 17.9133C5.41294 17.9627 5.37383 18.0214 5.34723 18.086C5.32064 18.1506 5.3071 18.2198 5.30739 18.2896C5.30739 18.5773 5.5435 18.8134 5.83861 18.8134H12.1614C12.4565 18.8134 12.6926 18.5773 12.6926 18.2896C12.6929 18.2198 12.6794 18.1506 12.6528 18.086C12.6262 18.0214 12.5871 17.9627 12.5377 17.9133C12.4883 17.8639 12.4296 17.8248 12.365 17.7982C12.3004 17.7716 12.2312 17.7581 12.1614 17.7584ZM8.94466 11.4356H5.83861C5.5435 11.4356 5.30739 11.6643 5.30739 11.9594C5.30739 12.2545 5.5435 12.4832 5.83861 12.4832H8.94466C9.23977 12.4832 9.47587 12.2545 9.47587 11.9594C9.47587 11.6643 9.23977 11.4356 8.94466 11.4356ZM12.1614 13.5383H5.83861C5.76877 13.538 5.69956 13.5515 5.63498 13.5781C5.57039 13.6047 5.51172 13.6438 5.46233 13.6932C5.41294 13.7426 5.37383 13.8013 5.34723 13.8659C5.32064 13.9304 5.3071 13.9996 5.30739 14.0695C5.30739 14.3572 5.5435 14.5933 5.83861 14.5933H12.1614C12.4565 14.5933 12.6926 14.3572 12.6926 14.0695C12.6929 13.9996 12.6794 13.9304 12.6528 13.8659C12.6262 13.8013 12.5871 13.7426 12.5377 13.6932C12.4883 13.6438 12.4296 13.6047 12.365 13.5781C12.3004 13.5515 12.2312 13.538 12.1614 13.5383Z"
        fill="white"
        fillOpacity="0.5"
      />
    </svg>
  );
};
export const DefaultFileIcon = ({ width = 18, height = 24 }) => {
  return (
    <svg
      width={width}
      height={height}
      viewBox="0 0 18 24"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M3.14572 0H11.1728L17.9751 7.09007V20.8792C17.9751 22.5982 16.5733 24 14.8543 24H3.14572C1.41932 24 0.0249023 22.5982 0.0249023 20.8792V3.12082C0.0249023 1.39442 1.41932 0 3.14572 0Z"
        fill="#E2E7F0"
      />
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M11.1654 0V7.03106H17.9751L11.1654 0Z"
        fill="#9DACBA"
      />
    </svg>
  );
};
export const VideoIcon = ({ width = 18, height = 24 }) => {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      version="1.1"
      xmlnsXlink="http://www.w3.org/1999/xlink"
      width={width}
      height={height}
      x="0"
      y="0"
      viewBox="0 0 791.454 791.454"
      style={{ enableBackground: "new 0 0 512 512" }}
      xmlSpace="preserve"
      className=""
    >
      <g>
        <path
          fill="#FA0000"
          fillRule="evenodd"
          d="M202.808 0h264.609l224.265 233.758v454.661c0 56.956-46.079 103.035-102.838 103.035H202.808c-56.956 0-103.035-46.079-103.035-103.035V103.035C99.772 46.079 145.851 0 202.808 0z"
          clipRule="evenodd"
          opacity="1"
          data-original="#FA0000"
          className=""
        ></path>
        <g fill="#fff">
          <path
            fillRule="evenodd"
            d="M467.219 0v231.978h224.463z"
            clipRule="evenodd"
            opacity="1"
            fill="#ffffff302"
            data-original="#ffffff302"
          ></path>
          <path
            d="M395.826 359.141c-70.602 0-127.954 57.352-127.954 127.954S325.224 614.85 395.826 614.85s127.756-57.154 127.756-127.756-57.352-127.756-127.756-127.953zm53.792 131.909a10.489 10.489 0 0 1-4.153 4.153l-72.975 36.586c-4.549 2.175-10.086.396-12.261-4.153-.791-1.187-.989-2.571-.989-4.153v-72.975c0-5.142 4.153-9.097 9.097-9.097 1.384 0 2.769.198 4.153.989l72.975 36.389c4.549 2.372 6.328 7.712 4.153 12.261z"
            fill="#FFFFFF"
            opacity="1"
            data-original="#FFFFFF"
          ></path>
        </g>
      </g>
    </svg>
  );
};
export const ZipIcon = ({ width = 18, height = 24 }) => {
  return (
    <svg
      width={width}
      height={height}
      viewBox="0 0 18 24"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M3.14572 0H11.1728L17.9751 7.09007V20.8792C17.9751 22.5982 16.5733 24 14.8543 24H3.14572C1.41932 24 0.0249023 22.5982 0.0249023 20.8792V3.12082C0.0249023 1.39442 1.41932 0 3.14572 0Z"
        fill="#FFB11F"
      />
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M11.1654 0V7.03106H17.9751L11.1654 0Z"
        fill="white"
        fillOpacity="0.5"
      />
      <path
        d="M6.6354 12.4685V10.8306C6.6354 10.6536 6.49521 10.5134 6.31815 10.5134H5.43281C5.25575 10.5134 5.11557 10.6536 5.11557 10.8306V12.4685L4.62126 13.9146C4.48107 14.3203 4.54748 14.7704 4.79832 15.1172C5.04179 15.4639 5.44755 15.6705 5.87548 15.6705C6.30341 15.6705 6.70181 15.4639 6.95265 15.1172C7.20349 14.7704 7.2699 14.3204 7.12971 13.9146L6.6354 12.4685ZM6.42884 14.7409C6.178 15.1024 5.573 15.1024 5.31479 14.7409C5.18937 14.5638 5.15247 14.3277 5.22625 14.1211L5.38857 13.6342H6.35506L6.52475 14.1211C6.59116 14.3277 6.56162 14.5638 6.42884 14.7409ZM3.91301 1.71903V2.61914C3.91301 2.7962 4.0532 2.93639 4.23026 2.93639H5.43285V4.27916H4.23026C4.0532 4.27916 3.91301 4.41935 3.91301 4.59641V5.28992C3.91301 5.46698 4.0532 5.61454 4.23026 5.61454H5.43285V6.94991H4.23026C4.0532 6.94991 3.91301 7.0901 3.91301 7.26716V7.96804C3.91301 8.1451 4.0532 8.28529 4.23026 8.28529H5.43285V9.30341C5.43285 9.48048 5.5804 9.62066 5.75746 9.62066H7.52077C7.69046 9.62066 7.83802 9.48048 7.83802 9.30341V8.60254C7.83802 8.43284 7.69046 8.28529 7.52077 8.28529H6.31081V6.94991H7.52077C7.69046 6.94991 7.83802 6.80973 7.83802 6.63266V5.93179C7.83802 5.75473 7.69046 5.61454 7.52077 5.61454H6.31081V4.27916H7.52077C7.69046 4.27916 7.83802 4.13161 7.83802 3.95455V3.26104C7.83802 3.08398 7.69046 2.93642 7.52077 2.93642H6.31081V1.71167L3.91301 1.71903Z"
        fill="white"
        fillOpacity="0.5"
      />
    </svg>
  );
};

export const NoMemberFound = ({ size = 24 }) => {
  return (
    <svg
      width={size}
      height={size}
      viewBox="0 0 24 24"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <g clipPath="url(#clip0_365_691)">
        <path
          d="M21.9871 0H3.23708C2.46168 0 1.83088 0.630843 1.83088 1.40625V15.7448C1.83088 16.5202 2.46168 17.151 3.23708 17.151H5.31894L1.1181 21.0917C0.795744 21.3941 0.614198 21.8092 0.607026 22.2604C0.599667 22.7225 0.78201 23.1742 1.10742 23.4996C1.43254 23.8249 1.88498 24.0074 2.34646 23.9999C2.79777 23.9927 3.21285 23.8112 3.51529 23.4888L8.77357 17.8834C11.3289 19.6494 15.1932 19.2988 17.3878 17.1511H21.9872C22.7626 17.1511 23.3934 16.5202 23.3934 15.7448V1.40625C23.3933 0.630843 22.7625 0 21.9871 0ZM2.83147 22.8473C2.70426 22.9829 2.52669 23.0593 2.33146 23.0624C2.1223 23.0657 1.91713 22.9835 1.77027 22.8366C1.62332 22.6897 1.54096 22.4851 1.54434 22.2753C1.54748 22.0802 1.62388 21.9027 1.75949 21.7755L5.91481 17.8775L6.7294 18.6921L2.83147 22.8473ZM7.37117 18.0079L6.59895 17.2357L7.29125 16.5863C7.51633 16.8468 7.76008 17.0906 8.02057 17.3156L7.37117 18.0079ZM6.51837 11.9948C6.51837 8.63469 9.25198 5.90108 12.6121 5.90108C20.6852 6.20741 20.683 17.7834 12.612 18.0886C9.25198 18.0886 6.51837 15.3549 6.51837 11.9948ZM21.9871 16.2135H18.2338C21.7106 11.7022 18.3556 4.91043 12.612 4.96363C7.08401 4.90972 3.6665 11.2737 6.72359 15.8334L6.31836 16.2135H3.23708C2.97862 16.2135 2.76838 16.0032 2.76838 15.7448V3.74999H10.5026C11.1238 3.72693 11.1234 2.83532 10.5026 2.81249H2.76838V1.40625C2.76838 1.14778 2.97862 0.937498 3.23708 0.937498H21.9871C22.2456 0.937498 22.4558 1.14778 22.4558 1.40625V2.81249H14.7213C14.1 2.83556 14.1005 3.72716 14.7213 3.74999H22.4558V15.7448C22.4558 16.0032 22.2455 16.2135 21.9871 16.2135Z"
          fill="black"
        />
        <path
          d="M12.6121 2.8125H12.6117C12.3529 2.8125 12.1431 3.02236 12.1431 3.28125C12.1662 3.90225 13.0579 3.9023 13.0808 3.28125C13.0808 3.02236 12.8709 2.8125 12.6121 2.8125ZM16.0716 10.0268L14.5801 8.53523C14.4055 8.35364 14.0917 8.35369 13.9171 8.53523L12.612 9.84037L11.3069 8.53528C11.1323 8.35369 10.8185 8.35369 10.6439 8.53528L9.15242 10.0268C8.96938 10.2098 8.96938 10.5067 9.15242 10.6898L10.4575 11.9948L9.15242 13.2999C8.96938 13.483 8.96938 13.7798 9.15242 13.9629L10.6439 15.4544C10.8185 15.636 11.1323 15.636 11.3069 15.4544L12.612 14.1493L13.9171 15.4545C14.0917 15.636 14.4055 15.636 14.5801 15.4545L16.0716 13.9629C16.2546 13.7799 16.2546 13.4831 16.0716 13.3L14.7665 11.9949L16.0716 10.6898C16.2547 10.5067 16.2547 10.2098 16.0716 10.0268ZM13.7721 11.6633C13.5891 11.8464 13.5891 12.1432 13.7721 12.3263L15.0772 13.6314L14.2486 14.46L12.9435 13.1549C12.7689 12.9734 12.4551 12.9734 12.2805 13.1549L10.9754 14.46L10.1468 13.6313L11.4519 12.3262C11.635 12.1432 11.635 11.8464 11.4519 11.6633L10.1468 10.3582L10.9754 9.52955L12.2805 10.8346C12.4551 11.0162 12.7689 11.0162 12.9435 10.8346L14.2486 9.5295L15.0772 10.3582L13.7721 11.6633Z"
          fill="black"
        />
      </g>
      <defs>
        <clipPath id="clip0_365_691">
          <rect width="24" height="24" fill="white" />
        </clipPath>
      </defs>
    </svg>
  );
};

export const RePostIcon = ({ size = 24, stroke = "#2D394A" }) => {
  return (
    <svg
      width={size}
      height={size}
      viewBox="0 0 50 50"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M24.014 5.27707C24.0215 5.49707 24.7095 6.57707 25.543 7.67707L27.058 9.67707L21.154 9.92707C14.2765 10.2181 12.569 10.9361 10.9895 14.1991C10.1515 15.9301 10 17.2891 10 23.0851V29.9271H12.5H15V23.2546C15 14.7016 14.887 14.8351 21.963 15.0341L27.0565 15.1771L25.542 17.1771C24.7095 18.2771 24.0215 19.3456 24.014 19.5521C24.0065 19.7586 25.2935 19.9171 26.875 19.9051L29.75 19.8826L32.343 16.1856L34.936 12.4886L32.3175 8.83307L29.6985 5.17707L26.8495 5.02707C25.282 4.94457 24.0065 5.05707 24.014 5.27707ZM35 26.5996C35 35.1526 35.113 35.0191 28.037 34.8201L22.9435 34.6771L24.458 32.6771C25.2905 31.5771 25.9785 30.5081 25.986 30.3021C25.9935 30.0961 24.7065 29.9371 23.125 29.9491L20.25 29.9716L17.657 33.6686L15.064 37.3656L17.6825 41.0211L20.3015 44.6771L23.1505 44.8271C24.718 44.9096 26 44.8511 26 44.6976C26 44.5441 25.3075 43.4641 24.4615 42.2976L22.923 40.1771L28.8365 39.9271C35.724 39.6361 37.4305 38.9196 39.0105 35.6551C39.8485 33.9241 40 32.5651 40 26.7691V19.9271H37.5H35V26.5996Z"
        fill={stroke}
      />
    </svg>
  );
};

export const SearchUserEmptyScreenIcon = () => {
  return (
    <svg
      width="151"
      height="150"
      viewBox="0 0 151 150"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M38.2715 27.3611C37.8832 27.361 37.5019 27.2577 37.1668 27.0616L22.424 18.4639C21.9206 18.1703 21.5544 17.6888 21.406 17.1253C21.2576 16.5617 21.3391 15.9623 21.6327 15.4589C21.9262 14.9555 22.4077 14.5893 22.9713 14.4409C23.5348 14.2925 24.1342 14.374 24.6377 14.6676L39.3804 23.2651C39.798 23.5083 40.1238 23.8823 40.3073 24.3294C40.4908 24.7764 40.5218 25.2715 40.3955 25.738C40.2693 26.2044 39.9927 26.6162 39.6088 26.9097C39.2248 27.2031 38.7548 27.3617 38.2715 27.3611ZM113.684 26.8038C113.201 26.8044 112.731 26.6457 112.347 26.3523C111.964 26.0589 111.687 25.6472 111.561 25.1808C111.435 24.7144 111.466 24.2194 111.649 23.7725C111.832 23.3255 112.158 22.9514 112.575 22.7081L126.619 14.5179C127.123 14.2243 127.722 14.1428 128.286 14.2912C128.849 14.4396 129.331 14.8058 129.624 15.3092C129.918 15.8126 130 16.412 129.851 16.9756C129.703 17.5391 129.337 18.0206 128.833 18.3142L114.789 26.5044C114.454 26.7004 114.073 26.8038 113.684 26.8038ZM31.7785 48.8796H31.7688L13.2608 48.799C12.678 48.7978 12.1197 48.565 11.7085 48.1521C11.5049 47.9476 11.3436 47.705 11.2338 47.4382C11.1239 47.1713 11.0677 46.8855 11.0684 46.5969C11.069 46.3084 11.1265 46.0228 11.2375 45.7565C11.3485 45.4901 11.5109 45.2482 11.7153 45.0447C12.1283 44.6335 12.6877 44.4032 13.2705 44.4045H13.2801L31.7881 44.4851C32.3709 44.4864 32.9293 44.7191 33.3404 45.1321C33.7516 45.545 33.9819 46.1044 33.9806 46.6872C33.9793 47.2699 33.7466 47.8283 33.3336 48.2395C32.9206 48.6506 32.3612 48.8809 31.7785 48.8796ZM75.2392 109.04H75.2295C74.941 109.038 74.6555 108.98 74.3894 108.869C74.1233 108.757 73.8818 108.594 73.6787 108.389C73.4755 108.184 73.3147 107.941 73.2055 107.674C73.0963 107.407 73.0407 107.121 73.0419 106.833L73.1196 89.0515C73.1249 87.838 74.1098 86.8466 75.3265 86.8639C75.6151 86.8652 75.9005 86.9232 76.1666 87.0348C76.4327 87.1464 76.6743 87.3094 76.8774 87.5143C77.0805 87.7192 77.2413 87.9622 77.3506 88.2292C77.4598 88.4963 77.5154 88.7823 77.5141 89.0708L77.4365 106.852C77.4338 107.433 77.2011 107.989 76.7893 108.399C76.3776 108.809 75.8202 109.039 75.2392 109.04ZM128.267 79.8435C127.879 79.8436 127.497 79.7402 127.162 79.5441L112.519 71.0049C112.016 70.7114 111.65 70.2298 111.501 69.6663C111.353 69.1028 111.435 68.5033 111.728 67.9999C112.022 67.4965 112.503 67.1303 113.067 66.9819C113.63 66.8335 114.23 66.9151 114.733 67.2086L129.376 75.7478C129.793 75.991 130.119 76.3651 130.302 76.8121C130.486 77.2591 130.517 77.7541 130.39 78.2204C130.264 78.6868 129.988 79.0985 129.604 79.392C129.22 79.6853 128.75 79.844 128.267 79.8435ZM139.593 43.3589C139.11 43.3584 138.64 43.1986 138.256 42.904C137.873 42.6095 137.597 42.1967 137.472 41.7297L131.059 17.7945C130.916 17.234 130.999 16.6397 131.291 16.1405C131.583 15.6412 132.061 15.2773 132.619 15.1276C133.178 14.9779 133.773 15.0546 134.276 15.3409C134.778 15.6272 135.148 16.1002 135.304 16.6571L141.717 40.5924C141.805 40.9181 141.816 41.2596 141.75 41.5903C141.684 41.921 141.543 42.2321 141.338 42.4996C141.132 42.7671 140.868 42.9837 140.566 43.1327C140.263 43.2817 139.931 43.3591 139.593 43.3589Z"
        fill="#DFF6FD"
      />
      <path
        d="M61.0554 91.4642C81.6473 98.1591 104.814 89.8148 116.075 70.3107C129.032 47.8673 121.311 19.159 98.8211 6.25017C76.3671 -6.63811 47.8697 1.03825 34.9249 23.4597C23.6693 42.9556 28.0182 67.1789 44.096 81.6665C45.8333 83.2318 46.0973 85.8597 44.6995 87.7344L32.9623 103.475L38.0187 111.804L47.7602 112.019L55.5235 93.9838C56.444 91.8454 58.8408 90.7444 61.0554 91.4642Z"
        fill="#0055A3"
      />
      <path
        d="M13.1653 130.027C8.69312 136.025 10.4565 144.582 16.9358 148.323C23.4151 152.064 31.7073 149.313 34.6654 142.441L47.7602 112.02L32.9624 103.477L13.1653 130.027Z"
        fill="#FD6930"
      />
      <path
        d="M113 46.875C113 52.9805 111.541 58.7455 108.952 63.8396C108.508 64.7142 106.827 64.6315 106.318 65.4647C106.063 65.8808 107.005 67.2287 106.735 67.6339C100.367 77.197 89.7233 83.6689 77.5347 84.3208C76.8611 84.3568 75.9605 82.8601 75.2779 82.8601C74.4852 82.8601 73.9206 84.3504 73.1401 84.3021C53.5285 83.0839 38 66.7931 38 46.875C38 26.1645 54.7895 9.375 75.5 9.375C88.4442 9.375 99.8565 15.9334 106.596 25.9084C107.026 26.5456 106.571 28.5316 106.963 29.1958C107.358 29.8658 108.599 29.2146 108.953 29.9104C111.541 35.0045 113 40.7695 113 46.875Z"
        fill="#C8EFFE"
      />
      <path
        d="M85.9444 10.8516C95.0432 17.6938 100.928 28.5806 100.928 40.8416C100.928 61.5521 84.1383 78.3416 63.4277 78.3416C59.8943 78.3445 56.3781 77.8484 52.9834 76.868C58.6655 81.1409 65.601 83.8365 73.1399 84.305C73.9204 84.3533 74.7076 82.1807 75.5001 82.1807C76.1827 82.1807 76.8609 84.3598 77.5345 84.3237C89.7231 83.6722 100.367 77.2002 106.735 67.6368C107.205 66.9302 105.895 66.2068 106.317 65.4677C106.623 64.9339 108.673 64.3922 108.952 63.8426C111.541 58.7484 113 52.9834 113 46.8779C113 40.7725 111.541 35.0074 108.952 29.9133C108.508 29.0388 107.472 30.0322 106.963 29.1987C106.709 28.7827 107.005 26.5239 106.735 26.119C101.883 18.8338 94.551 13.3424 85.9444 10.8516Z"
        fill="#99E6FC"
      />
      <path
        d="M9.875 56.25C15.0527 56.25 19.25 52.0527 19.25 46.875C19.25 41.6973 15.0527 37.5 9.875 37.5C4.69733 37.5 0.5 41.6973 0.5 46.875C0.5 52.0527 4.69733 56.25 9.875 56.25Z"
        fill="#01C0FA"
      />
      <path
        d="M141.125 56.25C146.303 56.25 150.5 52.0527 150.5 46.875C150.5 41.6973 146.303 37.5 141.125 37.5C135.947 37.5 131.75 41.6973 131.75 46.875C131.75 52.0527 135.947 56.25 141.125 56.25Z"
        fill="#80D261"
      />
      <path
        d="M75.5 121.875C80.6777 121.875 84.875 117.678 84.875 112.5C84.875 107.322 80.6777 103.125 75.5 103.125C70.3223 103.125 66.125 107.322 66.125 112.5C66.125 117.678 70.3223 121.875 75.5 121.875Z"
        fill="#01C0FA"
      />
      <path
        d="M18.667 23.4375C23.8447 23.4375 28.042 19.2402 28.042 14.0625C28.042 8.88483 23.8447 4.6875 18.667 4.6875C13.4893 4.6875 9.29199 8.88483 9.29199 14.0625C9.29199 19.2402 13.4893 23.4375 18.667 23.4375Z"
        fill="#01C0FA"
      />
      <path
        d="M132.333 23.4375C137.511 23.4375 141.708 19.2402 141.708 14.0625C141.708 8.88483 137.511 4.6875 132.333 4.6875C127.155 4.6875 122.958 8.88483 122.958 14.0625C122.958 19.2402 127.155 23.4375 132.333 23.4375Z"
        fill="#01C0FA"
      />
      <path
        d="M132.333 89.0625C137.511 89.0625 141.708 84.8652 141.708 79.6875C141.708 74.5098 137.511 70.3125 132.333 70.3125C127.155 70.3125 122.958 74.5098 122.958 79.6875C122.958 84.8652 127.155 89.0625 132.333 89.0625Z"
        fill="#01C0FA"
      />
      <path
        d="M73.1394 84.3028L73.2821 51.5545C73.2846 50.9734 73.5173 50.417 73.9291 50.007C74.3409 49.5971 74.8983 49.3669 75.4794 49.3669H75.489C75.7776 49.3682 76.0631 49.4263 76.3292 49.5378C76.5953 49.6494 76.8368 49.8124 77.0399 50.0173C77.243 50.2222 77.4038 50.4652 77.5131 50.7322C77.6223 50.9993 77.6779 51.2853 77.6766 51.5738L77.5339 84.3219C76.0697 84.3998 74.6043 84.3939 73.1394 84.3028ZM69.7908 49.0484C70.2227 49.0484 70.6451 48.9211 71.0051 48.6823C71.3651 48.4435 71.6467 48.104 71.8148 47.7061C71.9828 47.3081 72.0299 46.8695 71.95 46.445C71.8702 46.0205 71.6669 45.6289 71.3658 45.3192C71.635 45.0923 71.8457 44.8041 71.9802 44.4788C72.1147 44.1534 72.169 43.8005 72.1387 43.4498C72.1083 43.0991 71.9942 42.7608 71.8058 42.4634C71.6174 42.166 71.3603 41.9182 71.0561 41.7409L44.265 26.1172C43.4524 27.3375 42.7121 28.6044 42.0478 29.9114L67.309 44.6431L38.0731 44.5159C37.9823 45.979 37.9761 47.445 38.0544 48.9104L69.7814 49.0484H69.7908ZM77.3444 45.8003C77.4897 46.0497 77.6826 46.2679 77.9123 46.4427C78.142 46.6174 78.4038 46.7452 78.6829 46.8187C78.962 46.8921 79.2528 46.9099 79.5387 46.871C79.8247 46.832 80.1001 46.7371 80.3494 46.5916L108.952 29.9114C108.287 28.6044 107.547 27.3375 106.734 26.1172L78.1357 42.7953C77.8864 42.9407 77.6682 43.1337 77.4936 43.3634C77.3189 43.593 77.1912 43.8549 77.1177 44.1339C77.0442 44.413 77.0264 44.7037 77.0653 44.9897C77.1042 45.2756 77.199 45.551 77.3444 45.8003ZM108.952 63.8407L80.7759 47.4096C80.2725 47.116 79.6731 47.0345 79.1096 47.1829C78.546 47.3313 78.0645 47.6975 77.771 48.2009C77.4774 48.7043 77.3959 49.3037 77.5443 49.8673C77.6927 50.4308 78.0588 50.9123 78.5623 51.2059L106.734 67.635C107.547 66.4147 108.287 65.1478 108.952 63.8407Z"
        fill="#F2FBFF"
      />
      <path
        d="M108.952 63.8407L97.2642 57.0248C96.6304 58.3473 95.9195 59.6314 95.1352 60.8706L106.735 67.635C107.547 66.4147 108.288 65.1478 108.952 63.8407ZM77.5342 84.3216L77.5723 75.5806C76.1375 76.1653 74.6677 76.66 73.1713 77.0615L73.1396 84.3031C74.603 84.3937 76.0685 84.4001 77.5342 84.3216ZM108.952 29.9114C108.288 28.6044 107.547 27.3375 106.735 26.1172L99.4456 30.3679C99.8707 31.8327 100.209 33.3342 100.454 34.867L108.952 29.9114Z"
        fill="#C8EFFE"
      />
      <path
        d="M75.5 56.25C80.6777 56.25 84.875 52.0527 84.875 46.875C84.875 41.6973 80.6777 37.5 75.5 37.5C70.3223 37.5 66.125 41.6973 66.125 46.875C66.125 52.0527 70.3223 56.25 75.5 56.25Z"
        fill="#80D261"
      />
    </svg>
  );
};

export const DuplicateIcon = ({ size = 24, stroke = "#2D394A" }) => {
  return (
    <svg
      width={size}
      height={size}
      viewBox="0 0 24 24"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M9 15C9 12.1716 9 10.7574 9.87868 9.87868C10.7574 9 12.1716 9 15 9L16 9C18.8284 9 20.2426 9 21.1213 9.87868C22 10.7574 22 12.1716 22 15V16C22 18.8284 22 20.2426 21.1213 21.1213C20.2426 22 18.8284 22 16 22H15C12.1716 22 10.7574 22 9.87868 21.1213C9 20.2426 9 18.8284 9 16L9 15Z"
        stroke={stroke}
        strokeWidth="2"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M16.9999 9C16.9975 6.04291 16.9528 4.51121 16.092 3.46243C15.9258 3.25989 15.7401 3.07418 15.5376 2.90796C14.4312 2 12.7875 2 9.5 2C6.21252 2 4.56878 2 3.46243 2.90796C3.25989 3.07417 3.07418 3.25989 2.90796 3.46243C2 4.56878 2 6.21252 2 9.5C2 12.7875 2 14.4312 2.90796 15.5376C3.07417 15.7401 3.25989 15.9258 3.46243 16.092C4.51121 16.9528 6.04291 16.9975 9 16.9999"
        stroke={stroke}
        strokeWidth="2"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  );
};

export const HierarchyIcon = ({ size = 18 }) => {
  return (
    <svg
      width={size}
      height={size}
      viewBox="0 0 18 18"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M14.2233 2.97335L13.114 1.86396C12.9719 1.72192 12.9009 1.65089 12.8168 1.60193C12.7585 1.56797 12.6959 1.54203 12.6307 1.52481C12.5366 1.5 12.4362 1.5 12.2353 1.5C11.3146 1.5 10.8543 1.5 10.51 1.69522C10.2742 1.82898 10.079 2.02417 9.94522 2.26003C9.75 2.60426 9.75 3.0646 9.75 3.98528V4.875C9.75 5.92835 9.75 6.45502 10.0028 6.83336C10.1122 6.99714 10.2529 7.13777 10.4166 7.2472C10.795 7.5 11.3217 7.5 12.375 7.5C13.4283 7.5 13.955 7.5 14.3334 7.2472C14.4971 7.13777 14.6378 6.99714 14.7472 6.83336C15 6.45502 15 5.92208 15 4.85621C15 4.38105 15 4.14347 14.9323 3.92589C14.9052 3.83874 14.8702 3.75426 14.8278 3.67347C14.7218 3.47178 14.5556 3.30563 14.2233 2.97335Z"
        stroke="#2D394A"
        strokeWidth="1.125"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M14.2233 11.9733L13.114 10.864C12.9719 10.7219 12.9009 10.6509 12.8168 10.6019C12.7585 10.568 12.6959 10.542 12.6307 10.5248C12.5366 10.5 12.4362 10.5 12.2353 10.5C11.3146 10.5 10.8543 10.5 10.51 10.6952C10.2742 10.829 10.079 11.0242 9.94522 11.26C9.75 11.6043 9.75 12.0646 9.75 12.9853V13.875C9.75 14.9283 9.75 15.455 10.0028 15.8334C10.1122 15.9971 10.2529 16.1378 10.4166 16.2472C10.795 16.5 11.3217 16.5 12.375 16.5C13.4283 16.5 13.955 16.5 14.3334 16.2472C14.4971 16.1378 14.6378 15.9971 14.7472 15.8334C15 15.455 15 14.9221 15 13.8562C15 13.381 15 13.1435 14.9323 12.9259C14.9052 12.8387 14.8702 12.7543 14.8278 12.6735C14.7218 12.4718 14.5556 12.3056 14.2233 11.9733Z"
        stroke="#2D394A"
        strokeWidth="1.125"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M7.5 4.5H3M3 4.5V1.5M3 4.5V9C3 11.1213 3 12.182 3.65901 12.841C4.31802 13.5 5.37868 13.5 7.5 13.5"
        stroke="#2D394A"
        strokeWidth="1.125"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  );
};

export const StoryLikeIcon = ({ size = 16, ...svgProps }) => {
  return (
    <svg
      width={size}
      height={size}
      viewBox="0 0 16 16"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...svgProps}
    >
      <rect width="16" height="16" rx="8" fill="#EF3B41" />
      <mask
        id="mask0_11896_33218"
        style={{
          maskType: "alpha",
        }}
        maskUnits="userSpaceOnUse"
        x="0"
        y="0"
        width="17"
        height="16"
      >
        <rect x="0.0078125" width="16" height="16" fill="#D9D9D9" />
      </mask>
      <g mask="url(#mask0_11896_33218)">
        <path
          d="M7.89696 11.2587C7.7722 11.3011 7.56671 11.3011 7.44195 11.2587C6.37781 10.9088 4 9.44929 4 6.9755C4 5.8835 4.91369 5 6.04022 5C6.70806 5 7.29884 5.31099 7.66945 5.79161C8.04007 5.31099 8.63452 5 9.29869 5C10.4252 5 11.3389 5.8835 11.3389 6.9755C11.3389 9.44929 8.9611 10.9088 7.89696 11.2587Z"
          fill="#F5F7F8"
        />
      </g>
    </svg>
  );
};
