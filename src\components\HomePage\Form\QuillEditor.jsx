"use client";

import { useEffect, useRef } from "react";
import "quill/dist/quill.snow.css";
import {
  followingList,
  getMentionUserAndProject,
  getProject,
  getTeamUsers,
} from "@/app/action";
import { generateAvatarCanvas } from "@/utils/function";
import { safeToast } from "@/utils/safeToast";

export default function QuillEditor({
  setValues,
  formik,
  value,
  generatedDescription,
  loginUserData,
}) {
  const editorRef = useRef(null);
  const quillInstanceRef = useRef(null);
  const skipUpdateRef = useRef(false);

  const getMentionList = async (searchQuery) => {
    // console.log(searchQuery, "search query");
    let queryParams = {
      searchQuery: searchQuery?.trim(),
    };

    const mentionList = getMentionUserAndProject(queryParams);

    return Promise.all([mentionList]);

    // console.log(userRes, projectRes);
  };

  useEffect(() => {
    let quillInstance;

    const handleKeyDown = (e) => {
      if (e.key === "Enter" || e.key === "Backspace") {
        const selection = quillInstance.getSelection();
        if (selection) {
          const formats = quillInstance.getFormat(selection);
          setTimeout(() => {
            quillInstance.formatLine(selection.index + 1, 1, formats);
            quillInstance.format("bold", true);
          }, 0);
        }
      }
    };
    // Extract the YouTube video ID (supports watch, youtu.be, shorts, embed)
    function getYouTubeId(text) {
      const m = text.match(
        /(?:https?:\/\/)?(?:www\.)?(?:youtube\.com\/(?:watch\?v=|shorts\/|embed\/)|youtu\.be\/)([A-Za-z0-9_-]{11})/
      );
      return m ? m[1] : null;
    }

    const loadEditor = async () => {
      const Quill = (await import("quill")).default;

      const { Mention, MentionBlot } = await import("quill-mention");
      await import("quill-mention/dist/quill.mention.css");
      class CustomMentionBlot extends MentionBlot {
        static create(data) {
          const node = super.create(data);

          // Replace inner content with anchor tag if link exists
          if (data.link) {
            node.innerHTML = "";
            const a = window.document.createElement("a");
            a.setAttribute("href", data.link);
            a.setAttribute("target", "_blank");
            a.setAttribute("data-user-slug", data?.userSlug);
            a.setAttribute("id", data?.id);
            a.setAttribute("name", data.value);
            a.innerText = `@${data.value}`;
            node.appendChild(a);
          } else {
            node.innerText = `@${data.value}`;
          }

          return node;
        }
      }

      // Register mention module
      Quill.register(
        {
          "modules/mention": Mention,
          "formats/mention": CustomMentionBlot,
        },
        true
      );

      if (editorRef.current) {
        quillInstance = new Quill(editorRef.current, {
          theme: "snow",
          modules: {
            toolbar: {
              container: [
                [{ header: [1, 2, 3, false] }],
                ["bold", "italic", "underline"],
                [{ list: "ordered" }, { list: "bullet" }],
                [{ align: [] }],
                ["link", "image", "video"],
                [{ color: [] }],
              ],
              handlers: {
                image: function () {
                  const quill = this.quill;

                  // Count current images in editor
                  const currentImages =
                    quill.root.querySelectorAll("img").length;
                  // if (currentImages >= 6) {
                  //   safeToast.error("You can only upload up to 6 images.");
                  //   return;
                  // }

                  // Create file input
                  const input = document.createElement("input");
                  input.setAttribute("type", "file");
                  input.setAttribute("accept", "image/*");
                  input.setAttribute("multiple", true);

                  input.onchange = () => {
                    const files = Array.from(input.files);

                    // If total would exceed 6, block all uploads
                    if (files.length + currentImages > 6) {
                      safeToast.error("You can select a maximum of 6 images");
                      return;
                    }

                    // Proceed to insert images
                    files.forEach((file) => {
                      const reader = new FileReader();
                      reader.onload = (e) => {
                        const range = quill.getSelection(true);

                        // Insert the image
                        quill.insertEmbed(
                          range.index,
                          "image",
                          e.target.result
                        );

                        // Insert a line break after each image (small gap)
                        quill.insertText(range.index + 1, "\n");

                        // Optional: style images with margin via CSS
                        const imgElements = quill.root.querySelectorAll("img");
                        imgElements.forEach((img) => {
                          img.style.margin = "1.25rem auto"; // 5px top & bottom spacing
                          img.style.display = "block";
                        });

                        quill.setSelection(range.index + 2);
                      };
                      reader.readAsDataURL(file);
                    });
                  };

                  input.click();
                },
              },
            },

            mention: {
              allowedChars: /^[A-Za-z\s]*$/,
              mentionDenotationChars: ["@"],
              dataAttributes: [
                "id",
                "value",
                "denotationChar",
                "link",
                "userSlug",
                "image",
              ],
              renderLoading: () => {
                return "Loading...";
              },
              renderItem: function (item) {
                const container = document.createElement("div");
                container.style.display = "flex";
                container.style.alignItems = "center";
                container.style.padding = ".35rem";

                const img = document.createElement("img");
                img.src = item.image ?? generateAvatarCanvas(item.value, 100);
                img.alt = item.value;
                img.style.width = "2.5rem";
                img.style.height = "2.5rem";
                img.style.borderRadius = "50%";
                img.style.marginRight = ".7rem";
                img.style.objectFit = "cover";

                const textContainer = document.createElement("div");

                const nameDiv = document.createElement("div");
                nameDiv.style.fontSize = "1rem";
                nameDiv.style.lineHeight = "1.5rem";
                nameDiv.style.fontWeight = 500;
                nameDiv.innerText = item.value;

                textContainer.appendChild(nameDiv);

                container.appendChild(img);
                container.appendChild(textContainer);

                return container;
              },

              source: function (searchTerm, renderList) {
                getMentionList(searchTerm)
                  .then(([res]) => {
                    console.log(res);
                    const list = [
                      ...res?.data?.users?.map((ele) => ({
                        id: ele?.id,
                        value: `${ele?.firstName} ${ele?.lastName ?? ""}`,
                        link: `/user/${ele?.slug}`,
                        image: ele?.image,
                      })),
                      ...res?.data?.projects?.map((ele) => ({
                        id: ele?.id,
                        value: ele?.name,
                        link: `/projects/${ele?.slug}`,
                        userSlug: ele?.User?.id,
                        image: ele?.image,
                      })),
                    ];
                    renderList(list, searchTerm);
                  })
                  .catch((error) => {
                    // console.error("Mention fetch error:", error);
                    renderList([], searchTerm);
                  });
              },
            },
          },
        });
        quillInstance.format("bold", true);
        // Set initial value if provided
        if (value) {
          quillInstance.clipboard.dangerouslyPasteHTML(value);
          const length = quillInstance.getLength();
          quillInstance.setSelection(length - 1, 0);
        }

        const handleTextChange = (delta, oldDelta, source) => {
          console.log("text change");
          if (skipUpdateRef.current) {
            skipUpdateRef.current = false;
            return;
          }

          if (source === "user") {
            // 🔹 Check for YouTube links and replace with label
            const insertedText =
              delta.ops?.map((op) => op.insert).join("") || "";
            console.log("Inserted text:", insertedText);

            // Check if the inserted text contains YouTube URL
            const youtubeRegex = /https?:\/\/(www\.)?(youtube\.com|youtu\.be)/;
            if (youtubeRegex.test(insertedText)) {
              console.log(
                "YouTube link detected, replacing with label",
                insertedText
              );

              const youtubeId = getYouTubeId(insertedText);
              const embed = `<iframe
                     class="ql-video"
                     frameborder="0"
                     allowfullscreen="true"
                     src="https://www.youtube.com/embed/${youtubeId}?showinfo=0">
                   </iframe><p><br></p>`;
              console.log(youtubeId, "youtube id");
              // Get current content
              const currentHtml = quillInstance.root.innerHTML;

              // Replace YouTube URLs with the iframe
              const updatedHtml = currentHtml.replace(
                /https?:\/\/(www\.)?(youtube\.com|youtu\.be)[^\s<]*/g,
                embed
              );

              // Update the editor content
              skipUpdateRef.current = true;
              quillInstance.clipboard.dangerouslyPasteHTML(updatedHtml);

              // Wait for content to be inserted, then position cursor after iframe
              setTimeout(() => {
                // Find the iframe in the editor and position cursor after it
                const iframes = quillInstance.root.querySelectorAll("iframe");
                if (iframes.length > 0) {
                  const lastIframe = iframes[iframes.length - 1];
                  const iframeIndex = quillInstance.getIndex(lastIframe);

                  // Position cursor after the iframe with some spacing
                  quillInstance.insertText(iframeIndex + 1, "\n\n", "user");
                  quillInstance.setSelection(iframeIndex + 3, 0);
                } else {
                  // Fallback: position at end
                  const newLength = quillInstance.getLength();
                  quillInstance.setSelection(newLength, 0);
                }
              }, 100);
              return;
            }

            // 🔹 Handle image uploads (add newline after image)
            delta.ops?.forEach((op) => {
              if (op.insert && op.insert.image) {
                const selection = quillInstance.getSelection();
                if (selection) {
                  setTimeout(() => {
                    quillInstance.insertText(selection.index + 1, "\n", "user");
                    quillInstance.setSelection(selection.index + 2, 0);
                  }, 0);
                }
              }
            });
          }

          // 🔹 Always sync HTML with formik + state
          const html = quillInstance.root.innerHTML;
          console.log(html);
          formik?.setFieldValue("description", html);
          setValues?.((prev) => ({
            ...prev,
            description: html,
          }));
        };

        quillInstance.on("selection-change", (range) => {
          if (range) {
            const formats = quillInstance.getFormat(range);
            if (!formats.bold) {
              quillInstance.format("bold", true);
            }
          }
        });
        quillInstance.on("text-change", handleTextChange);

        // quillInstance.on("editor-change", (eventName, ...args) => {
        //   if (eventName === "text-change") {
        //     console.log(args);
        //     const [delta, oldDelta, source] = args;

        //     // Only react to user actions (not programmatic inserts)
        //     if (source === "user") {
        //       delta.ops?.forEach((op, i) => {
        //         if (op.insert && op.insert.image) {
        //           // Find the index where the image was inserted
        //           let index = 0;
        //           for (let j = 0; j < i; j++) {
        //             index += delta.ops[j].insert?.length || 1;
        //           }

        //           // Move cursor after the image and insert newline
        //           quillInstance.insertText(index + 1, "\n", "user");
        //           quillInstance.setSelection(index + 2, 0);
        //         }
        //       });
        //     }
        //   }
        // });

        quillInstance.root.addEventListener("keydown", handleKeyDown);

        quillInstanceRef.current = quillInstance;
      }
    };

    loadEditor();

    return () => {
      if (quillInstance) {
        quillInstance.off("text-change");
        quillInstance.off("selection-change");
        // quillInstance.off("editor-change");
        quillInstance.root.removeEventListener("keydown", handleKeyDown);
      }
    };
  }, []);

  // 🔹 Insert generatedDescription when it changes
  useEffect(() => {
    const quillInstance = quillInstanceRef.current;
    if (quillInstance && generatedDescription) {
      const selection = quillInstance.getSelection(true); // focus editor if not focused
      const index = selection ? selection.index : quillInstance.getLength(); // insert at cursor or end
      const boldHTML = `<strong>${generatedDescription}</strong>`;
      // Insert as HTML or plain text as needed
      quillInstance.clipboard.dangerouslyPasteHTML(index, boldHTML);

      // Move cursor to end of inserted text
      const newLength = quillInstance.getLength();
      quillInstance.setSelection(newLength - 1, 0);
    }
  }, [generatedDescription]);

  return <div ref={editorRef} />;
}
